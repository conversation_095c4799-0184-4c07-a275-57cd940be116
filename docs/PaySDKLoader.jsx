import React, {useEffect} from 'react';

const unloadWidget = (win: Window, instanceName) => {
  // Check if the widget is already loaded
  if (!win[`loaded-${instanceName}`]) {
    console.warn(`Widget with name [${instanceName}] is not loaded.`);
    return;
  }

  // Remove the target widget element if it exists
  const targetElement = win.document.getElementById(`widget-${instanceName}`);
  if (targetElement) {
    targetElement.remove();
    console.log(`Removed widget element [${instanceName}] from the DOM.`);
  }

  // Remove the script tag associated with the widget
  const scriptElement = win.document.getElementById(instanceName);
  if (scriptElement) {
    scriptElement.remove();
    console.log(`Removed script element [${instanceName}] from the DOM.`);
  }

  // Clean up global window properties
  delete win[`loaded-${instanceName}`];
  delete win[instanceName];

  console.log(`Unloaded widget instance [${instanceName}] and removed from window object.`);
};

const DEFAULT_NAME = '_treezpay';

const PaySDKLoader = ({totalAmount, entityId, locationId, authTokenFactory, handlePaymentResponse}) => {
  useEffect(() => {
    // Inject the script
    (function (w, d, s, o, f, js, fjs) {
      w[o] = w[o] || function () { (w[o].q = w[o].q || []).push(arguments) };
      js = d.createElement(s), fjs = d.getElementsByTagName(s)[0];
      js.id = o; js.src = f; js.async = 1; fjs.parentNode.insertBefore(js, fjs);
    }(window, document, 'script', '_treezpay', 'https://treezpaydev.s3.us-west-2.amazonaws.com/paysdk/widget.js'));
    // Script initialization
    window._treezpay('init', {
      element: document.querySelector('#paysdk-widget'),
      debug: true,
      authTokenFactory,
      entityId, // Entity ID from input
      locationId, // optional Location ID
      theme: {
        positionFixed: false,
      },
    });
    setTimeout(() => {
      window._treezpay('event', 'payment', {
        handlePaymentResponse: async (response) => {
          console.log(response);
          handlePaymentResponse(response);
        },
        originalAmount: totalAmount * 100,
        referenceNumber: '48916c04-e0dc-4c19-8d5e-4e76d4069661',
        employeeReferenceId: '777',
        customer: {
          id: '1',
          firstName: 'Max',
          lastName: 'Test',
          phone: '5109889644',
          email: '<EMAIL>',
        },
        ticketId: 'ticket-b721-19199c977958',
        ticketAlphaNumId: 'test2-TICKET-xx7x-2',
        entityId: 'entityId',
        locationId: 'locationId' || '', // Optional location ID
        paymentMethod: 'paymentMethod' || '', // Optional payment method
        entityName: 'entityName' || '' // optional entity name
    });
    }, 500);

    // Cleanup function to remove _treezpay when the component unmounts
    return () => {
      unloadWidget(window, DEFAULT_NAME);
    };
  }, []); // Empty array ensures this effect runs only once when the component mounts

  return (
    <div id="paysdk-widget" />
  );
};

export default PaySDKLoader;
