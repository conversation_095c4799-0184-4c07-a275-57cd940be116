{
  "compilerOptions": {
    "module": "commonjs",
    "target": "es6",
    "lib": ["es6", "dom"],
    "moduleResolution": "node",
    "rootDir": "./",
    "sourceMap": true,
    "allowJs": false,
    "noImplicitAny": true,
    "noUnusedLocals": true,
    "noImplicitThis": true,
    "strictNullChecks": true,
    "noImplicitReturns": true,
    "preserveConstEnums": true,
    "suppressImplicitAnyIndexErrors": true,
    "forceConsistentCasingInFileNames": true,
    "outDir": ".build",
    "jsx": "react",
    "jsxFactory": "h",
    "resolveJsonModule": true,
    "jsxFragmentFactory": "Fragment", // For Preact fragments
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true
  },
  "exclude": ["node_modules"],
  "types": ["typePatches"]
}
