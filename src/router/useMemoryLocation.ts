import { useState, useCallback } from 'preact/hooks';

// Custom in-memory location hook implementing BaseLocationHook
export const useMemoryLocation = ({ path = "/" } = {}) => {
  const [currentPath, setCurrentPath] = useState(path);
  const [history, setHistory] = useState([path]);

  // The `navigate` function changes the path and records history
  const navigate = useCallback((to: string | number) => {
    if (typeof to === 'number') {
      // Handle "go back" by number of steps
      const newIndex = (history.length - 1) + to;
      if (newIndex >= 0 && newIndex < history.length) {
        const newPath = history[newIndex];
        setCurrentPath(newPath);
        setHistory(history.slice(0, newIndex + 1));
      }
    } else {
      // Handle normal path navigation
      setCurrentPath(to);
      if (to === '/') {
        setHistory(['/']);
      } else {
        setHistory([...history, to]);
      }
    }
  }, [history]);

  // The hook for the `Router` expects an array with [currentPath, navigate]
  const memoryLocationHook = useCallback(() => [currentPath, navigate], [currentPath, navigate]);

  return { hook: memoryLocationHook, history, navigate, currentPath };
};
