import { IProcessorFlow, IProcessorIntegrationType } from '../types/processor';

export const ProcessorIntegrationType = {
    SALE: 'SALE',
    PAYMENT_LINK: 'PAYMENTLINK',
}

export const ProcessorFlows = {
    TERMINAL_SALE: 'terminal_sale',
    PAYMENT_LINK_QR: 'payment_link_qr',
    PAYMENT_LINK_REDIRECT: 'payment_link_redirect',
    PAYMENT_LINK_SMS: 'payment_link_sms',
}

export const FlowToIntegrationTypeMap: Record<IProcessorFlow, IProcessorIntegrationType> = {
    [ProcessorFlows.TERMINAL_SALE]: ProcessorIntegrationType.SALE,
    [ProcessorFlows.PAYMENT_LINK_QR]: ProcessorIntegrationType.PAYMENT_LINK,
    [ProcessorFlows.PAYMENT_LINK_REDIRECT]: ProcessorIntegrationType.PAYMENT_LINK,
    [ProcessorFlows.PAYMENT_LINK_SMS]: ProcessorIntegrationType.SALE,
};
