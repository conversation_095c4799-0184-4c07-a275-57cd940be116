import {
  registerDecorator,
  validateSync,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';
import { Payment } from '../models';

function OrderAmountMatchesOriginalAmount(payment: Payment) {
  if (!payment.order) return true; // If no order, no need to validate this condition

  const itemsTotal = (payment?.order?.items || []).reduce(
    (sum, item) => sum + item.totalAmount,
    0
  );

  const totalAmount = payment.order?.taxAmount + itemsTotal;

  return totalAmount === payment?.originalAmount;
}

export const IsOrderAmountValid = (validationOptions?: ValidationOptions) => {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isOrderAmountValid',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const payment = args.object as Payment;
          // Ensure that the order is present and already validated
          if (payment.order) {
            const errors = validateSync(payment.order);
            if (errors.length > 0) {
              return false;
            }
          }
          return OrderAmountMatchesOriginalAmount(payment);
        },
        defaultMessage(args: ValidationArguments) {
          return 'The sum of taxAmount and total amounts of items must equal the originalAmount.';
        },
      },
    });
  };
};
