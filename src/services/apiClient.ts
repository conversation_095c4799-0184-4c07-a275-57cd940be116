import axios, { AxiosError, AxiosInstance } from 'axios';
import * as Sentry from '@sentry/react';
import {
  CreateInvoiceResponse,
  EntityConfigurationsResponse,
  InvoiceRequest,
  ListPaymentDeviceLocationsResponse,
  MSOTreezPayBaseResponse,
  MSOTreezPayCancelPaymentResponse,
  MSOTreezPayCaptureChargeRequest,
  MSOTreezPayCaptureChargeResponse,
  MSOTreezPayConnectionStatusResponse,
  MSOTreezPayCreatePaymentResponse,
  MSOTreezPayPaymentRequest,
  PreviewRequest,
  PreviewResponse,
  ProcessorsResponse,
  WidgetApi,
  MSOTreezPayPaymentStatusResponse,
  MSOTreezPayInvoiceStatusResponse,
  Channel,
} from '../models';
import { generateUUID } from './strings';
import { parseAxiosError } from '../utils/apiUtils';
import Logger from './logger';

interface ApiClientOptions {
  baseUrl: string;
  /**
   * An optional factory, that should supply bearer token which will
   * be attached to authorization header when making requests.
   */
  authTokenFactory?: () => Promise<string | undefined>;
  /**
   * Write more logs into console.
   */
  debug?: boolean;
  getEntityId: () => Promise<string | undefined>;
  setApiError: (error: any) => void;
}

interface ApiRequest<TRequest = any> {
  readonly url: string;
  readonly method?: 'GET' | 'DELETE' | 'POST' | 'PUT';
  readonly requestData?: TRequest;
}

export class ApiClient implements WidgetApi {
  private readonly client: AxiosInstance;
  private readonly logger: Logger;
  /**
   * When true, payment status polling will be performed at the Gateway layer.
   */
  private readonly POLL_DB_ONLY: boolean = false;

  constructor(options: ApiClientOptions) {
    if (!options?.baseUrl) {
      throw new Error('baseUrl is required');
    }

    this.client = axios.create({
      baseURL: options.baseUrl,
    });

    this.logger = new Logger({ debug: options.debug });

    this.client.interceptors.response.use(
      response => {
        return response;
      },
      (error: AxiosError) => {
        const { response } = error;
        const captureStatusCode = [400, 401, 403, 404, 422];

        if (
          response?.status &&
          (response.status >= 500 ||
            captureStatusCode.includes(response.status))
        ) {
          Sentry.captureException(error, {
            level: 'error',
            extra: {
              url: response.config.url,
              method: response.config.method,
              status: response.status,
              statusText: response.statusText,
              headers: response.config.headers,
              requestData: response.config.data,
              responseData: response.data,
            },
            tags: {
              api: response.config.url,
              httpStatus: response.status,
            },
          });
        }

        this.logger.error(
          `Failed to call API`,
          error.response?.status,
          error.response?.data
        );
        options.setApiError(parseAxiosError(error));
        return Promise.reject(error);
      }
    );
    if (options.debug) {
      this.useDebugLogs();
    }

    if (options.authTokenFactory) {
      this.useAuth(
        options.authTokenFactory,
        options.getEntityId,
        options.debug
      );
    }
  }

  public getProcessors = async (channel: Channel) =>
    await this.callApi<ProcessorsResponse>({ url: `/sdk/processors/${channel}` });

  public getPaymentDeviceLocations = async (processorNames: string[]) =>
    await this.callApi<ListPaymentDeviceLocationsResponse>({
      url: `/paymentDeviceLocations?posLocationEnabled=true&processorNames=${processorNames.join(',')}`,
    });

  public getEntityConfigurations = async () =>
    await this.callApi<EntityConfigurationsResponse>({
      url: '/entityConfigurations',
    });

  public getPaymentDeviceStatus = async (paymentDeviceLocationId: string) =>
    await this.callApi<
      MSOTreezPayBaseResponse<MSOTreezPayConnectionStatusResponse>
    >({ url: `/paymentDeviceLocations/${paymentDeviceLocationId}/status` });

  public captureCharge = async (captureChargeRequest: MSOTreezPayCaptureChargeRequest) =>
    await this.callApi<MSOTreezPayBaseResponse<MSOTreezPayCaptureChargeResponse>>({
      url: '/charges/capture',
      method: 'POST',
      requestData: captureChargeRequest,
    });

  public getPreview = async (
    previewRequest: PreviewRequest
  ): Promise<PreviewResponse> =>
    await this.callApi<Promise<PreviewResponse>>({
      url: '/payments/preview',
      method: 'POST',
      requestData: previewRequest,
    });

  public getPaymentStatus = async (
    paymentId: string
  ): Promise<MSOTreezPayBaseResponse<MSOTreezPayPaymentStatusResponse>> =>
    await this.callApi<
      MSOTreezPayBaseResponse<MSOTreezPayPaymentStatusResponse>
    >({ url: `/payments/${paymentId}/status?pollDbOnly=${this.POLL_DB_ONLY}` });

  public getInvoiceStatus = async (
    invoiceId: string
  ): Promise<MSOTreezPayBaseResponse<MSOTreezPayInvoiceStatusResponse>> =>
    await this.callApi<
      MSOTreezPayBaseResponse<MSOTreezPayInvoiceStatusResponse>
    >({ url: `/invoices/${invoiceId}/status` });

  public createPayment = async (
    paymentRequest: MSOTreezPayPaymentRequest
  ): Promise<MSOTreezPayBaseResponse<MSOTreezPayCreatePaymentResponse>> =>
    await this.callApi<
      MSOTreezPayBaseResponse<MSOTreezPayCreatePaymentResponse>
    >({ url: `/payments`, method: 'POST', requestData: paymentRequest });

  public cancelPayment = async (
    paymentId: string
  ): Promise<MSOTreezPayBaseResponse<MSOTreezPayCancelPaymentResponse>> =>
    await this.callApi<
      MSOTreezPayBaseResponse<MSOTreezPayCancelPaymentResponse>
    >({ url: `/payments/${paymentId}/cancel`, method: 'PUT' });

  public createInvoice = async (
    invoiceRequest: InvoiceRequest
  ): Promise<CreateInvoiceResponse> =>
    await this.callApi<Promise<CreateInvoiceResponse>>({
      url: `/invoices`,
      method: 'POST',
      requestData: invoiceRequest,
    });

  /**
   * Helper with saint defaults to perform an HTTP call.
   * @param request A request to perform.
   */
  private callApi<TResponse = any, TRequest = any>(
    request: ApiRequest<TRequest>
  ): Promise<TResponse> {
    return new Promise((resolve, reject) => {
      this.client
        .request<TResponse>({
          url: request.url,
          method: request.method ?? 'GET',
          data: request.requestData,
          responseType: 'json',
          headers: {
            'X-Created-By': `PaySDK ${process.env.GIT_TAG}` || 'PaySDK',
          },
        })
        .then(response =>
          response?.status && response.status >= 200 && response.status < 400
            ? resolve(response?.data)
            : reject(response?.data)
        )
        .catch((error: AxiosError) => {
          reject(error.response ?? error.message);
        });
    });
  }

  private useDebugLogs() {
    this.client.interceptors.request.use(config => {
      this.logger.debug('Calling API', config.url, config.params);
      return config;
    });

    this.client.interceptors.response.use(
      response => {
        this.logger.debug(
          'Got response from API',
          response.config.url,
          response.data
        );
        return response;
      },
      (error: AxiosError) => {
        this.logger.error(
          'There was an error calling API',
          error.request?.url,
          error.response?.status,
          error.message
        );
        return Promise.reject(error);
      }
    );
  }

  private useAuth(
    tokenFactory: () => Promise<string | undefined>,
    getEntityId: () => Promise<string | undefined>,
    debug?: boolean
  ) {
    this.client.interceptors.request.use(async config => {
      const token = await tokenFactory();
      const entityId = await getEntityId();
      if (token) {
        config.headers.authorization = `Bearer ${token}`;
        config.headers.idempotencyid = generateUUID();
        config.headers['Entity-Id'] = entityId;
      } else if (debug) {
        this.logger.warn(
          'No token returned by factory, skipping Authorization header'
        );
      }

      return config;
    });
  }
}
