interface LoggerOptions {
  debug?: boolean;
}

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

class Logger {
  private debugEnabled: boolean;

  constructor(options: LoggerOptions = {}) {
    this.debugEnabled = options.debug || false;
  }

  log(message: string, level: LogLevel = 'info'): void {
    if (level === 'debug' && !this.debugEnabled) {
      return;
    }
    switch (level) {
      case 'debug':
        console.debug(message);
        break;
      case 'info':
        console.info(message);
        break;
      case 'warn':
        console.warn(message);
        break;
      case 'error':
        console.error(message);
        break;
      default:
        console.log(message);
    }
  }

  debug(message?: any, ...optionalParams: any[]): void {
    if (this.debugEnabled) {
      console.debug(message, ...optionalParams);
    }
  }

  info(message?: any, ...optionalParams: any[]): void {
    console.info(message, ...optionalParams);
  }

  warn(message: string): void {
    console.warn(message);
  }

  error(message?: any, ...optionalParams: any[]): void {
    console.error(message, ...optionalParams);
  }
}

export default Logger;
