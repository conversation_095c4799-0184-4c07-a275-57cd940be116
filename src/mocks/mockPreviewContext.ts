import { PreviewContextProps } from '../context/PreviewProvider';

export const mockPreviewContext: PreviewContextProps = {
  loading: false,
  fetchPreviewPaymentTotals: jest.fn(),
  reset: jest.fn(),
  previewTotals: {
    totalAmount: 1250,
    tipAmount: 200,
    feeAmount: 0,
    taxAmount: 0,
    cashbackAmount: 0,
    roundAmount: 500,
  },
};

export const resetMockPreviewContext = () => {
  mockPreviewContext.loading = false;
  mockPreviewContext.fetchPreviewPaymentTotals = jest.fn();
  mockPreviewContext.reset = jest.fn();
  // Reset other properties or mocks if necessary
};
