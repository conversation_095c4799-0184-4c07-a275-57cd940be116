import {
  Globals,
  MSOTreezPayCustomer,
  Payment,
  PaymentProcessor,
} from '../models';

type Path = string | number;
export type BaseLocationHook = (
  ...args: any[]
) => [Path, (path: Path, ...args: any[]) => any];

let mockCurrentPath: Path = '/tips';

const mockNavigate: (path: Path, ...args: any[]) => any = (
  path: Path,
  ...args: any[]
) => {
  if (typeof path === 'string') {
    mockCurrentPath = path;
  }
  // Handle other cases if necessary
};

const mockMemoryLocationHook: BaseLocationHook = (...args: any[]) => [
  mockCurrentPath,
  mockNavigate,
];

const mockCustomer: MSOTreezPayCustomer = {
  id: 123,
  firstName: 'Jane',
  lastName: 'Doe',
  phone: '1231112233',
  email: '<EMAIL>',
};

const mockPayment: Payment = {
  paymentId: 'payment123',
  handlePaymentResponse: jest.fn().mockResolvedValue(undefined),
  customer: mockCustomer,
  originalAmount: 1000,
  employeeReferenceId: 'emp123',
  ticketId: 'ticket123',
  ticketAlphaNumId: 'alpha123',
  tip: 200,
  taxes: 50,
  discount: 100,
  rewardDollars: 0,
  locationId: 'loc123',
  processorName: 'ProcessorX',
  cancel: false,
};

const mockPaymentProcessor: PaymentProcessor = {
  paymentMethod: 'ATM',
  locationId: 'loc123',
  processorId: 'proc123',
  processorName: 'ProcessorX',
  paymentDeviceConfigurationId: 'config123',
  paymentSent: false,
};

export const mockGlobalsContext: Globals = {
  widgetOpen: false,
  setWidgetOpen: jest.fn(),
  payment: mockPayment,
  setPayment: jest.fn(),
  paymentProcessor: mockPaymentProcessor,
  setPaymentProcessor: jest.fn(),
  overlayVisible: false,
  setOverlayVisible: jest.fn(),
  overlayMessage: '',
  setOverlayMessage: jest.fn(),
  setOverlayContext: jest.fn(),
  resetOverlay: jest.fn(),
  overlayContext: {},
  memoryLocationHook: mockMemoryLocationHook,
  navigate: mockNavigate,
  history: [],
  currentPath: mockCurrentPath,
  element: document.createElement('div'), // or `undefined` if you want to keep it optional
  apiError: null,
  setApiError: jest.fn(),
  showTotalDetails: false,
  setShowTotalDetails: jest.fn(),
  newTabRef: { current: { tab: null } },
  setMountedComponents: jest.fn(),
};

export const resetMockGlobalsContext = () => {
  mockCurrentPath = '/tips';
  mockGlobalsContext.currentPath = mockCurrentPath;
  mockGlobalsContext.overlayVisible = false;
  mockGlobalsContext.setOverlayVisible = jest.fn();
  mockGlobalsContext.setShowTotalDetails = jest.fn();
  // Reset other properties or mocks if necessary
};
