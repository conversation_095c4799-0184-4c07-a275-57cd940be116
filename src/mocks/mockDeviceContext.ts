import { DeviceContextProps } from '../context/DeviceProvider';

const mockPaymentDeviceLocation = {
  ATM: [
    {
      id: 'device-001',
      name: 'ATM Device 1',
      deviceregisterid: 'reg-001',
      deviceauthcode: 'auth-001',
      deviceauthcodeDecrypted: 'decrypted-auth-001',
      posLocationId: 'pos-001',
      printConfig: null, // Adjust as necessary
      createdAt: '2024-01-01T12:00:00Z',
      processorId: 'proc-001',
      processorName: 'ATM Processor',
      updatedAt: '2024-01-02T12:00:00Z',
      tpn: null, // Adjust as necessary
    },
  ],
  Debit: [
    {
      id: 'device-002',
      name: 'Debit Device 1',
      deviceregisterid: 'reg-002',
      deviceauthcode: 'auth-002',
      deviceauthcodeDecrypted: 'decrypted-auth-002',
      posLocationId: 'pos-002',
      printConfig: null, // Adjust as necessary
      createdAt: '2024-01-01T12:00:00Z',
      processorId: 'proc-002',
      processorName: 'Debit Processor',
      updatedAt: '2024-01-02T12:00:00Z',
      tpn: null, // Adjust as necessary
    },
  ],
};

export const mockDeviceContext: DeviceContextProps = {
  paymentMethods: {
    ATM: ['ATM Processor 1'],
    Debit: ['Debit Processor 1'],
  },
  paymentMethodByDeviceLocation: mockPaymentDeviceLocation,
  paymentMethodFeeMapping: {},
  getPaymentMethodFee: jest.fn(),
  getPaymentMethodRoundingAmount: jest.fn(),
  getPaymentMethodByProcessorId: jest.fn(),
  getProcessorNameByProcessorId: jest.fn(),
  isPreTippingEnabled: jest.fn().mockReturnValue(true),
  loading: false,
  refetch: jest.fn(),
};

export const mockPaymentDeviceLocationWithMultipleDevices = {
  ATM: [
    {
      id: 'device-001',
      name: 'ATM Device 1',
      deviceregisterid: 'reg-001',
      deviceauthcode: 'auth-001',
      deviceauthcodeDecrypted: 'decrypted-auth-001',
      posLocationId: 'pos-001',
      printConfig: null,
      createdAt: '2024-01-01T12:00:00Z',
      processorId: 'proc-001',
      processorName: 'ATM Processor',
      updatedAt: '2024-01-02T12:00:00Z',
      tpn: null,
    },
    {
      id: 'device-002',
      name: 'ATM Device 2',
      deviceregisterid: 'reg-002',
      deviceauthcode: 'auth-002',
      deviceauthcodeDecrypted: 'decrypted-auth-002',
      posLocationId: 'pos-002',
      printConfig: null,
      createdAt: '2024-01-01T12:00:00Z',
      processorId: 'proc-002',
      processorName: 'ATM Processor 2',
      updatedAt: '2024-01-02T12:00:00Z',
      tpn: null,
    },
  ],
  Debit: [
    {
      id: 'device-003',
      name: 'Debit Device 1',
      deviceregisterid: 'reg-003',
      deviceauthcode: 'auth-003',
      deviceauthcodeDecrypted: 'decrypted-auth-003',
      posLocationId: 'pos-003',
      printConfig: null,
      createdAt: '2024-01-01T12:00:00Z',
      processorId: 'proc-003',
      processorName: 'Debit Processor 1',
      updatedAt: '2024-01-02T12:00:00Z',
      tpn: null,
    },
    {
      id: 'device-004',
      name: 'Debit Device 2',
      deviceregisterid: 'reg-004',
      deviceauthcode: 'auth-004',
      deviceauthcodeDecrypted: 'decrypted-auth-004',
      posLocationId: 'pos-004',
      printConfig: null,
      createdAt: '2024-01-01T12:00:00Z',
      processorId: 'proc-004',
      processorName: 'Debit Processor 2',
      updatedAt: '2024-01-02T12:00:00Z',
      tpn: null,
    },
  ],
};
