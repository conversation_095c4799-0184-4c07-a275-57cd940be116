import { h } from 'preact';
import useNavigateBack from '../useNavigateBack';
import { GlobalsContext } from '../../AppContext'; // Ensure the path matches the location of your AppContext file
import { DeviceContext } from '../../context/DeviceProvider';
import { renderHook } from '@testing-library/preact';
import { mockGlobalsContext } from '../../mocks/mockGlobalsContext';
import {
  mockDeviceContext,
  mockPaymentDeviceLocationWithMultipleDevices,
} from '../../mocks/mockDeviceContext';

describe('useNavigateBack', () => {
  const mockNavigate = jest.fn();
  const mockHistory = ['/home', '/atm', '/debit'];

  const renderNavigateBackHook = () => {
    const globalsContext = {
      ...mockGlobalsContext,
      history: mockHistory,
      navigate: mockNavigate,
    };
    return renderHook(() => useNavigateBack(), {
      wrapper: ({ children }) => (
        <GlobalsContext.Provider value={globalsContext}>
          <DeviceContext.Provider value={mockDeviceContext}>
            {children}
          </DeviceContext.Provider>
        </GlobalsContext.Provider>
      ),
    });
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('navigates to home if there is no previous route', () => {
    const emptyHistory = ['/home'];
    const globalsContext = {
      ...mockGlobalsContext,
      history: emptyHistory,
      navigate: mockNavigate,
    };
    const { result } = renderHook(() => useNavigateBack(), {
      wrapper: ({ children }) => (
        <GlobalsContext.Provider value={globalsContext}>
          <DeviceContext.Provider value={mockDeviceContext}>
            {children}
          </DeviceContext.Provider>
        </GlobalsContext.Provider>
      ),
    });

    result.current();
    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  test('navigates to home if previous route is /atm and only one ATM method is available', () => {
    const { result } = renderNavigateBackHook();

    result.current(); // Trigger the hook
    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  test('navigates to home if previous route is /debit and only one Debit method is available', () => {
    const { result } = renderNavigateBackHook();

    result.current(); // Trigger the hook
    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  test('navigates to previous route if not an ATM or Debit condition', () => {
    const customHistory = ['/home', '/checkout'];
    const globalsContext = {
      ...mockGlobalsContext,
      history: customHistory,
      navigate: mockNavigate,
    };

    const { result } = renderHook(() => useNavigateBack(), {
      wrapper: ({ children }) => (
        <GlobalsContext.Provider value={globalsContext}>
          <DeviceContext.Provider value={mockDeviceContext}>
            {children}
          </DeviceContext.Provider>
        </GlobalsContext.Provider>
      ),
    });

    result.current();
    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  test('navigates to /atm when there is more than one ATM device', () => {
    const globalsContext = {
      ...mockGlobalsContext,
      history: mockHistory,
      navigate: mockNavigate,
    };
    const { result } = renderHook(() => useNavigateBack(), {
      wrapper: ({ children }) => (
        <GlobalsContext.Provider value={globalsContext}>
          <DeviceContext.Provider
            value={{
              ...mockDeviceContext,
              paymentMethodByDeviceLocation:
                mockPaymentDeviceLocationWithMultipleDevices,
            }}
          >
            {children}
          </DeviceContext.Provider>
        </GlobalsContext.Provider>
      ),
    });

    result.current(); // Trigger the hook
    expect(mockNavigate).toHaveBeenCalledWith(-1); // Should navigate back, not to '/'
  });

  test('navigates to /debit when there is more than one Debit device', () => {
    const customHistory = ['/home', '/debit'];
    const globalsContext = {
      ...mockGlobalsContext,
      history: customHistory,
      navigate: mockNavigate,
    };
    const { result } = renderHook(() => useNavigateBack(), {
      wrapper: ({ children }) => (
        <GlobalsContext.Provider value={globalsContext}>
          <DeviceContext.Provider
            value={{
              ...mockDeviceContext,
              paymentMethodByDeviceLocation:
                mockPaymentDeviceLocationWithMultipleDevices,
            }}
          >
            {children}
          </DeviceContext.Provider>
        </GlobalsContext.Provider>
      ),
    });

    result.current(); // Trigger the hook
    expect(mockNavigate).toHaveBeenCalledWith(-1); // Should navigate back, not to '/'
  });
});
