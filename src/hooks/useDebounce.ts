import { useState, useEffect } from 'preact/hooks';

/**
 * A custom hook that allows you to debounce any fast changing value.
 * @param {T} value - The value to be debounced.
 * @param {number} delay - The amount of time to wait before updating the value.
 * @returns The debounced value.
 */
const useDebounce = <T>(value: T, delay = 500) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value]);

  return debouncedValue;
};

export default useDebounce;
