import { h } from 'preact';
import { useContext } from 'preact/hooks';
import { GlobalsContext } from '../AppContext';
import Button from '../components/buttons/button';
import useResetPayment from './useResetPayment';
import useLogger from './useLogger';

export interface PaymentActionButtonsProps {
  customRetryText?: string;
  customChangeMethodText?: string;
}

export const usePaymentActionButtons = ({
  customRetryText = 'Retry',
  customChangeMethodText = 'Change payment method',
}: PaymentActionButtonsProps) => {
  const {
    setOverlayVisible,
    setOverlayContext,
    setApiError,
    navigate,
    payment,
    paymentProcessor,
    setPaymentProcessor,
  } = useContext(GlobalsContext);
  const resetPayment = useResetPayment();
  const logger = useLogger();

  const handleRetry = () => {
    if (!payment) {
      logger.error('No payment object available');
      return;
    }

    if (paymentProcessor) {
      setPaymentProcessor({
        ...paymentProcessor,
        paymentSent: false,
      });
    }

    navigate('/paymentProcessing/');
  };

  const handleChangePaymentMethod = () => {
    setOverlayVisible(false);
    setOverlayContext(null);
    setApiError(null);
    resetPayment();
    navigate('/');
  };

  return {
    primaryActionButton: (
      <Button
        variant="primary"
        text={customRetryText}
        disabled={false}
        onClick={handleRetry}
      />
    ),
    changePaymentMethodButton: (
      <Button
        variant="secondary"
        text={customChangeMethodText}
        disabled={false}
        onClick={handleChangePaymentMethod}
      />
    ),
  };
};
