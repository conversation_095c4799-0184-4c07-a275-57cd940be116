import { useCallback, useContext } from 'preact/hooks';
import { GlobalsContext } from '../AppContext';
import { DeviceContext, DeviceContextProps } from '../context/DeviceProvider';

const useNavigateBack = () => {
  const { history, navigate } = useContext(GlobalsContext);

  const deviceContext = useContext(DeviceContext);

  const { paymentMethodByDeviceLocation, paymentMethods } =
    deviceContext as DeviceContextProps;

  const navigateBack = useCallback(() => {
    if (history.length < 2) {
      // Navigate to home if history has fewer than 2 routes
      navigate('/');
      return;
    }

    const previousRoute = history[history.length - 2];

    const shouldNavigateHome = (method: string) => {
      if (previousRoute !== `/${method.toLowerCase()}`) {
        return false;
      }

      if (method === 'ACH') {
        return (paymentMethods[method]?.length || 0) === 1;
      }

      return (paymentMethodByDeviceLocation[method]?.length || 0) === 1;
    };

    if (
      !previousRoute ||
      shouldNavigateHome('ATM') ||
      shouldNavigateHome('Debit') ||
      shouldNavigateHome('Credit') ||
      shouldNavigateHome('ACH')
    ) {
      navigate('/');
    } else {
      navigate(-1);
    }
  }, [history, paymentMethodByDeviceLocation, navigate]);

  return navigateBack;
};

export default useNavigateBack;
