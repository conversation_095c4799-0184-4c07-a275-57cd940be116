import { useContext } from 'preact/hooks';
import { ConfigContext, GlobalsContext, ServiceContext } from '../AppContext';
import { DeviceContext } from '../context/DeviceProvider';
import { InvoiceRequest } from '../models';
import useLogger from './useLogger';
import { ProcessorIntegrationType } from '../constants';

const getFeeAmount = (
  processorName: string,
  paymentMethodFeeMapping: any
): number => {
  if (!processorName || !paymentMethodFeeMapping) return 0;
  return processorName.toLowerCase().startsWith('aeropay')
    ? Number(paymentMethodFeeMapping[processorName] ?? 0)
    : 0;
};

export const useGeneratePaymentLink = () => {
  const logger = useLogger();
  const config = useContext(ConfigContext);
  const service = useContext(ServiceContext);
  const deviceContext = useContext(DeviceContext);
  const { payment } = useContext(GlobalsContext);

  const generatePaymentLink = async (
    processorName: string,
    paymentMethod: string,
    type = ProcessorIntegrationType.SALE
  ) => {
    if (!payment || !deviceContext) {
      throw new Error('Payment or device context not available');
    }

    const {
      ticketAlphaNumId,
      originalAmount,
      taxes,
      customer,
      order: orderData,
    } = payment;

    const feeAmount = getFeeAmount(
      processorName,
      deviceContext.paymentMethodFeeMapping
    );

    const invoiceRequest: InvoiceRequest = {
      associateId: '98c90070-ce81-4d74-a25f-da257424a4a4',
      sessionId: config.sessionId,
      method: paymentMethod,
      type,
      ticketAlphaNumId,
      referenceNumber: ticketAlphaNumId,
      originalAmount,
      feeAmount,
      taxAmount: taxes || 0,
      paymentOptions: [
        {
          method: paymentMethod,
          processor: processorName,
        },
      ],
      order: orderData || {
        taxAmount: 0,
        items: [
          {
            name: 'item 1',
            totalAmount: originalAmount,
            quantity: 1,
          },
        ],
      },
    };

    if (customer) {
      const { phone, email, firstName, lastName } = customer;
      invoiceRequest.senderPhone = phone;
      invoiceRequest.senderEmail = email;
      invoiceRequest.senderFirstName = firstName;
      invoiceRequest.senderLastName = lastName;
      invoiceRequest.customer = customer;
    }

    const result = await service?.createInvoice(invoiceRequest);

    if (
      type === ProcessorIntegrationType.PAYMENT_LINK &&
      !result?.data.paymentLinkUrls[0]?.url
    ) {
      throw new Error('No payment link URL received');
    }

    return result;
  };

  return {
    generatePaymentLink,
    getFeeAmount,
  };
};
