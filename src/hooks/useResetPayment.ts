import { useContext } from 'preact/hooks';
import { GlobalsContext } from '../AppContext';
import { PreviewContext } from '../context/PreviewProvider';
import { Payment } from '../models';

const useResetPayment = () => {
  const { setPayment, setPaymentProcessor } = useContext(GlobalsContext);

  const previewContext = useContext(PreviewContext);

  const resetPayment = () => {
    setPayment(
      prevPayment =>
        ({
          ...prevPayment,
          tip: 0,
          processorName: undefined,
          paymentId: undefined,
          invoiceId: undefined,
        }) as Payment
    );

    if (previewContext) {
      previewContext.reset();
    }

    setPaymentProcessor(undefined);
  };

  return resetPayment;
};

export default useResetPayment;
