import { useContext } from 'preact/hooks';
import { DeviceContext } from '../context/DeviceProvider';
import { IPaymentMethod, IProcessorFlowConfig } from '../types/processor';

/**
 * A custom hook to retrieve list of supported payment flows, grouped by payment method or for a particular processor.
 * @param method - required payment method name
 * @param processorName - optional processor name
 * @returns - a list of flows for a given processor OR a list of processor-flow array for the given method
 */
export const useFlow = (
  method: IPaymentMethod,
  processorName?: string
): string[] | IProcessorFlowConfig[] => {
  const { processorFlowConfigByMethod } = useContext(DeviceContext)!;

  const methodConfig = processorFlowConfigByMethod[method] || [];

  if (!processorName) {
    return methodConfig;
  }

  const processorEntry = methodConfig.find(p => p.processorName === processorName);
  return processorEntry?.flows || [];
};
