import { useMemo } from 'preact/hooks';
import { Payment } from '../models';

export const usePaymentDevice = (payment: Payment, paymentMethodByDeviceLocation) => {
    return useMemo(() => {
        if (!payment || !payment.locationId || !payment.processorName) return null;

        const combinedPaymentDeviceLocations = Object.values(paymentMethodByDeviceLocation).flat();
        return combinedPaymentDeviceLocations.find(
          paymentDevice =>
            paymentDevice.processorName === payment.processorName &&
            (paymentDevice.posLocationId || paymentDevice.locationId) ===
              payment.locationId
        );
    }, [payment, paymentMethodByDeviceLocation]);
};
