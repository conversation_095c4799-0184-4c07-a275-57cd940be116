import 'reflect-metadata';

import { h, createContext, ComponentChildren } from 'preact';
import {
  AppConfigurations,
  WidgetApi,
  Globals,
  Payment,
  PaymentProcessor,
  Preview,
  MountableComponentConfig,
  ConsumerType,
} from './models';
import { useEffect, useRef, useState } from 'preact/hooks';
import { ApiClient } from './services/apiClient';
import { DeviceProvider } from './context/DeviceProvider';
import WidgetEventListener from './components/listener/WidgetEventListener';
import { ThemeProvider } from './context/ThemeProvider';
import { useMemoryLocation } from './router/useMemoryLocation';
import { PreviewProvider } from './context/PreviewProvider';
import { plainToClass } from 'class-transformer';
import { isJWT, validate } from 'class-validator';
import { printValidationErrors } from './utils/validatorUtils';
import { EventDispatcherProvider } from './context/EventDispatcherProvider';
import { ParsedErrorDetails } from './utils/apiUtils';
import { WebSocketProvider } from './context/WebSocketProvider';
import useLogger from './hooks/useLogger';
import MountableComponentsRenderer from './components/mountable/renderer';

const noop = () => {};

export const ConfigContext = createContext<AppConfigurations>(
  {} as AppConfigurations
);
export const ServiceContext = createContext<WidgetApi | undefined>(undefined);
export const GlobalsContext = createContext<Globals>({
  widgetOpen: false,
  setWidgetOpen: noop,
  payment: undefined,
  setPayment: noop,
  paymentProcessor: undefined,
  setPaymentProcessor: noop,
  overlayVisible: false,
  setOverlayVisible: noop,
  overlayMessage: '',
  setOverlayMessage: noop,
  setOverlayContext: noop,
  overlayContext: {},
  resetOverlay: noop,
  memoryLocationHook: undefined,
  navigate: noop,
  history: undefined,
  currentPath: '',
  element: undefined,
  apiError: null,
  setApiError: noop,
  showTotalDetails: false,
  setShowTotalDetails: noop,
  newTabRef: { current: { tab: null } },
  setMountedComponents: noop,
});

interface Props {
  children: ComponentChildren;
  config: AppConfigurations;
  element?: HTMLElement;
}
export const AppContext = ({ children, config, element }: Props) => {
  const logger = useLogger();

  const {
    hook: memoryLocationHook,
    navigate,
    history,
    currentPath,
  } = useMemoryLocation({
    path: '/',
  });

  const newTabRef = useRef<{ tab: Window | null; url?: string | null }>({
    tab: null,
  });

  const [apiError, setApiError] = useState<ParsedErrorDetails | null>(null);
  const [serviceBaseUrl, setServiceBaseUrl] = useState<string | null>(null);

  useEffect(() => {
    const checkTokenAndSetUrl = async () => {
      try {
        const authToken = await config.authTokenFactory();
        const tokenIsJWT = isJWT(authToken);
        console.log('tokenIsJWT', tokenIsJWT);
        if (tokenIsJWT) {
          setServiceBaseUrl(config.serviceBaseUrl);
          config.consumerType = ConsumerType.USER;
        } else {
          config.consumerType = ConsumerType.PARTNER;
          console.log(
            'config.serviceBaseUrl',
            process.env.EXTERNAL_TP_GATEWAY_API_ENDPOINT ||
              'https://payment-gateway-api.dev.treezpay.com/v0/payment'
          );

          setServiceBaseUrl(
            process.env.EXTERNAL_TP_GATEWAY_API_ENDPOINT ||
              'https://payment-gateway-api.dev.treezpay.com/v0/payment'
          );
        }
      } catch (error) {
        logger.error('Error getting auth token:', error);
        config.consumerType = ConsumerType.USER;
        setServiceBaseUrl(config.serviceBaseUrl);
      }
    };

    checkTokenAndSetUrl();
  }, [config]);

  const services = useRef<ApiClient | null>(null);

  // Initialize ApiClient when serviceBaseUrl is determined
  useEffect(() => {
    if (!serviceBaseUrl) {
      return;
    }
    services.current = new ApiClient({
      baseUrl: serviceBaseUrl || '',
      debug: config.debug,
      authTokenFactory: () => config.authTokenFactory(),
      getEntityId: () => config.getEntityId(),
      setApiError: (error: ParsedErrorDetails) => setApiError(error),
    });
    // setServiceBaseUrl(config.serviceBaseUrl);
  }, [serviceBaseUrl, config]);

  // Don't render until config is ready
  if (!services.current) {
    console.log('Waiting for serviceBaseUrl...');
    return null;
  } else {
    console.log('serviceBaseUrl', serviceBaseUrl);
  }

  const [widgetOpen, setWidgetOpen] = useState(config.autoRenderPayments);
  const [payment, setPayment] = useState<Payment | undefined>(undefined);
  const [previewTotals, setPreviewTotals] = useState<Preview | null>(null);
  const [paymentProcessor, setPaymentProcessor] = useState<
    PaymentProcessor | undefined
  >(undefined);
  const [overlayVisible, setOverlayVisible] = useState<boolean>(false);
  const [overlayMessage, setOverlayMessage] = useState('');
  const [overlayContext, setOverlayContext] = useState({});
  const [showTotalDetails, setShowTotalDetails] = useState<boolean>(false);
  const [mountedComponents, setMountedComponents] = useState<
    Record<string, MountableComponentConfig>
  >({});

  // Effect to reset overlayContext when overlay is hidden
  useEffect(() => {
    if (!overlayVisible) {
      setOverlayContext({});
    }
  }, [overlayVisible]);

  const resetOverlay = () => {
    setOverlayVisible(false);
    setOverlayMessage('');
    setOverlayContext({});
  };

  useEffect(() => {
    element?.addEventListener(
      'widget-event',
      (e: CustomEvent<{ name?: string; args: any[] }>) => {
        switch (e.detail.name) {
          case 'open': {
            if (e.detail.args[1]) {
              logger.log(`widget-event open: ${e.detail.args[1]}`);
              config.payment = e.detail.args[1];
            }
            setWidgetOpen(true);
            break;
          }
          case 'payment': {
            const paymentData: Payment = e.detail.args[1];
            const paymentInstance = plainToClass(Payment, paymentData);
            console.log('originalAmount before :' + paymentData.originalAmount);
            paymentData.originalAmount = parseInt(
              String(paymentData.originalAmount)
            );
            console.log(
              'paymentData.originalAmount after :' + paymentData.originalAmount
            );
            validate(paymentInstance).then(errors => {
              if (errors.length > 0) {
                printValidationErrors(errors);
              } else {
                if (JSON.stringify(paymentData) !== JSON.stringify(payment)) {
                  navigate('/');
                  setPayment(paymentData);
                  setPaymentProcessor(undefined);
                  setPreviewTotals(null);
                  setOverlayVisible(false);
                  setApiError(null);
                }
              }
            });
            setWidgetOpen(true);
            break;
          }

          case 'cancel': {
            if (payment) {
              setPayment((prev: Payment) => ({
                ...prev,
                cancel: true,
              }));
            }
            break;
          }
          case 'changeScreen': {
            if (e.detail.args[1]) {
              const path = e.detail.args[1];
              navigate(path);
            }
            break;
          }
          case 'close':
            setWidgetOpen(false);
            break;
        }
      }
    );
  }, [element, payment]);

  return (
    <ConfigContext.Provider value={config}>
      <ServiceContext.Provider value={services.current}>
        <GlobalsContext.Provider
          value={{
            widgetOpen,
            setWidgetOpen,
            payment,
            setPayment,
            overlayMessage,
            setOverlayMessage,
            overlayVisible,
            setOverlayVisible,
            overlayContext,
            setOverlayContext,
            resetOverlay,
            memoryLocationHook,
            navigate,
            history,
            paymentProcessor,
            setPaymentProcessor,
            currentPath,
            element,
            apiError,
            setApiError,
            showTotalDetails,
            setShowTotalDetails,
            newTabRef,
            setMountedComponents,
          }}
        >
          <WebSocketProvider
            authTokenFactory={() => config?.authTokenFactory()}
            sessionId={config.sessionId || ''}
            setApiError={setApiError}
          >
            <EventDispatcherProvider>
              <DeviceProvider>
                <PreviewProvider
                  previewTotals={previewTotals}
                  setPreviewTotals={setPreviewTotals}
                >
                  <WidgetEventListener />
                  <ThemeProvider>
                    {children}
                    <MountableComponentsRenderer
                      mountedComponents={mountedComponents}
                    />
                  </ThemeProvider>
                </PreviewProvider>
              </DeviceProvider>
            </EventDispatcherProvider>
          </WebSocketProvider>
        </GlobalsContext.Provider>
      </ServiceContext.Provider>
    </ConfigContext.Provider>
  );
};
