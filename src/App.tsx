import { h } from 'preact';
import { Configurations } from './models';
import Main from './layout/Main';
import { AppContext } from './AppContext';
import PaymentStatusPollingProvider from './components/handlers/PaymentStatusPollingHandler';

type Props = Configurations;
export const App = ({ element, ...appSettings }: Props) => (
  <AppContext config={appSettings} element={element}>
    <PaymentStatusPollingProvider>
      <Main />
    </PaymentStatusPollingProvider>
  </AppContext>
);
