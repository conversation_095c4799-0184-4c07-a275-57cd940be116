.scope {
  /* app main */
  --tz-main-bg: #fff;

  /* colors */
  --tz-color-error: #d32f2f;
  --tz-color-disabled: #e9e9e9;
  --tz-color-border: #cccfd3;

  /* text */
  --tz-font-family: 'Inter';
  --tz-text-color-primary: #0F1709;
  --tz-text-color-secondary: #595959;

  /* defaults */
  --tz-line-height: 150%;
}

.reset {
  composes: scope;
  background: var(--tz-main-bg);
  color: var(--tz-text-color-primary);
  margin: 0;
}

.reset * {
  font-family: var(--tz-font-family);
  box-sizing: border-box;
}

.reset h1,
.reset h2,
.reset h3,
.reset h4,
.reset h5,
.reset h6 {
  line-height: var(--tz-line-height);
}

.reset ol li,
.reset ul li {
  padding: 0.2rem 0;
}

.reset p {
  margin: 0;
  padding: 0;
}

.reset small {
  color: var(--tz-text-color-secondary);
}

.reset a {
  color: var(--tz-text-color-primary);
  text-decoration: none;
}

.reset a:hover {
  text-decoration: none;
}

.reset form {
  display: block;
}

.reset form header {
  margin: 1.5rem 0;
  padding: 1.5rem 0;
}

.reset input,
.reset label,
.reset select,
.reset textarea {
  display: block;
  font-size: inherit;
}

.reset input,
.reset textarea {
  border: 1px solid var(--tz-color-border);
  padding: 0.4rem 0.8rem;
  width: 100%;
}

.reset label {
  font-weight: bold;
  margin-bottom: 0.2rem;
}
