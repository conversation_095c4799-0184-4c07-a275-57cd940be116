.scope {
  /* colors */
  --tz-color-disabled: #e9e9e9;

  /* text */
  --tz-text-color-primary: #0F1709;
  --tz-text-color-secondary: #595959;
  --tz-color-border: #E5E7E9
}

.root {
  composes: scope;
  width: 100%;
  background-color: transparent;
  position: relative;
}

.loader {
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 60px;
  background: rgba(255, 255, 255, 0.3);
}

.loader > * {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
