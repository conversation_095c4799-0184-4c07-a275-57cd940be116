.scope {
  /* layout */
  --tz-widget-height: 100vh;

  /* colors */
  --tz-color-error: #d32f2f;
  --tz-color-primary: #0055e5;
  --tz-color-primary-hover: #1738b8;
  --tz-color-disabled: #e9e9e9;
  --tz-color-border: #cccfd3;

  /* text */
  --tz-text-color-primary: #0F1709;
  --tz-text-color-secondary: #595959;
}

.root {
  composes: scope;
  position: relative;
  background-color: var(--tz-main-bg);
  overflow-y: auto;
  height: var(--tz-widget-height);
}

.rootFullScreen {
  position: fixed;
  top: 0;
  bottom: 0 !important;
  right: 0 !important;
  left: 0 !important;
}

.rootEmbed {
  width: 900px;
}

.scrollContainer {
  padding: 40px;
  position: relative;
  background: var(--tz-main-bg);
  margin: 0;
}

.container {
  padding: 40px;
  position: relative;
  background: var(--tz-main-bg);
  margin: 0;
  overflow-y: auto;
  height: 100%;
}

.poweredBy {
  display: flex;
  justify-content: center;
  bottom: 40px;
  position: absolute;
  text-align: center;
  align-items: center;
  width: 820px;
}

.poweredBy > *:first-child {
  margin-right: 5px;
}

.poweredByText {
  color: #2a2a2a;
  text-align: center;
  font-size: 10px;
  font-style: normal;
  font-weight: 500;
  line-height: 12px;
}

.headerButtons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}