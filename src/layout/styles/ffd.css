.scope {
  /* layout */
  --tz-padding-x: 80px;
  --tz-title-height: 57px;
  --tz-top-drawer-height: 132px;
  --tz-widget-height: 100vh;
  --tz-footer-height: 120px;

  /* colors */
  --tz-color-error: #d32f2f;
  --tz-color-primary: #a9e079;
  --tz-color-primary-hover: #8ccc52;
  --tz-color-disabled: #e9e9e9;
  --tz-color-border: #cccfd3;

  /* text */
  --tz-text-color-primary: #0F1709;
  --tz-text-color-secondary: #595959;
}

.root {
  composes: scope;
  position: relative;
  overflow: auto;
  height: var(--tz-widget-height);
}

@media (prefers-color-scheme: dark) {
  .scope:not(.noDark) {
    --tz-main-bg: #333333;
    --tz-text-color-primary: #fff;
    --tz-text-color-secondary: #666;
    --tz-color-error: #f44336;
  }
}

.rootFullScreen {
  position: fixed;
  top: 0;
  bottom: 0 !important;
  right: 0 !important;
  left: 0 !important;
}

.rootEmbed {
  width: 900px;
  border: 1px solid #F0F0F0;
}

.container {
  padding: 40px var(--tz-padding-x);
  position: relative;
  background: var(--tz-main-bg);
  margin: 0;
  overflow: auto;
  margin-top: calc(var(--tz-title-height));
  height: calc(var(--tz-widget-height) - var(--tz-title-height));
}

div.showTotalDisplay {
  margin-top: calc(var(--tz-top-drawer-height) + var(--tz-title-height));
  height: calc(var(--tz-widget-height) - var(--tz-top-drawer-height) - var(--tz-title-height));
}

.container.minimized {
  display: none;
}

.rootMinimised {
  height: auto;
}

/* landscape mode (width > height) */
@media (min-aspect-ratio: 1/1) {
  .rootFullScreen.scope {
    --tz-padding-x: 160px;
  }
}

/* portrait mode (height > width) */
@media (max-aspect-ratio: 1/1) {
  .rootFullScreen.scope {
    --tz-padding-x: 80px;
  }
}
