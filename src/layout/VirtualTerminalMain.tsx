import { h } from 'preact';
import { Route, Switch, Router } from 'wouter-preact';
import globalStyles from './styles/global.css';
import style from './styles/virtualTerminal.css';
import { useContext } from 'preact/hooks';
import { ConfigContext, GlobalsContext } from '../AppContext';
import clsx from 'clsx';
import AtmPayments from '../routes/AtmPayments';
import Overlay from '../components/Overlay';
import DebitPayments from '../routes/DebitPayments';
import { useTheme } from '../context/ThemeProvider';
import AsyncResponseEventHandler from '../routes/AsyncResponseEventHandler';
import CustomProcessorPaymentHandler from '../components/handlers/CustomProcessorPaymentHandler';
import { FunctionalComponent } from 'preact/src';
import QRCodeComponent from '../components/QRCode';
import PaymentProcessing from '../routes/PaymentProcessing';
import Checkout from '../routes/Checkout';
import ShowBackButton from '../components/backbutton/ShowBackButton';
import AppLoading from '../components/AppLoading';
import { DeviceContext } from '../context/DeviceProvider';
import ApiError from '../components/ApiError';
import { useWebSocket } from '../context/WebSocketProvider';
import CreditPayments from '../routes/CreditPayments';
import VTPaymentOptions from '../components/channels/virtualTerminal/paymentOptions';
import TPayIcon from '../assets/treezpay.svg';
import AchPayments from '../routes/AchPayments';
import ShowCloseButton from '../components/crossbutton/showCrossButton';
import { PaymentCloseEvent } from '../events/PaymentEvents';

const VirtualTerminalMain: FunctionalComponent = () => {
  const config = useContext(ConfigContext);
  const { isWebSocketEnabled } = config;
  const deviceContext = useContext(DeviceContext);
  const { isConnected } = useWebSocket();
  const { theme } = useTheme();
  const {
    overlayVisible,
    overlayMessage,
    overlayContext,
    resetOverlay,
    memoryLocationHook,
    apiError,
    currentPath,
    setWidgetOpen,
    payment,
    paymentProcessor,
  } = useContext(GlobalsContext);

  const PAYMENT_METHOD_PATHS = ['/ach', '/credit', '/atm', '/debit'];
  const STATIC_CONTAINER_ROUTES = ['/ach', '/'];
  const QR_ROUTES = ['/qr', '/qr/*'];

  const matchesPath = (currentPath: string, paths: string[]) =>
    paths.some(path => currentPath.includes(path));

  const isCloseButtonVisible = matchesPath(currentPath, PAYMENT_METHOD_PATHS);
  const isStaticContainerRoute = STATIC_CONTAINER_ROUTES.includes(currentPath);
  const isQRRoute = matchesPath(currentPath, QR_ROUTES);

  const handleCloseButton = () => {
    setWidgetOpen(false);
    const closeEvent: PaymentCloseEvent = {
      eventType: 'PAYMENT_CLOSE',
      userMessage: 'Widget closed by user',
      data: {},
      processorName: payment?.processorName || '',
      paymentMethod: paymentProcessor?.paymentMethod || '',
    };
    payment?.handlePaymentResponse(closeEvent);
  };

  return (
    <div
      className={clsx(
        style.root,
        globalStyles.reset,
        theme.positionFixed ? style.rootEmbed : style.rootFullScreen
      )}
    >
      {(deviceContext?.loading || (isWebSocketEnabled && !isConnected)) && (
        <AppLoading />
      )}
      {apiError && <ApiError />}
      {overlayVisible && (
        <Overlay
          context={overlayContext}
          message={overlayMessage}
          onCancel={() => {
            resetOverlay();
          }}
        />
      )}
      <AsyncResponseEventHandler />
      <CustomProcessorPaymentHandler />
      <div
        className={clsx(
          isStaticContainerRoute || isQRRoute
            ? style.container
            : style.scrollContainer,
          config.styles.classNameContainer
        )}
      >
        <div className={isCloseButtonVisible && style.headerButtons}>
          <ShowBackButton />
          <ShowCloseButton onClose={handleCloseButton} />
        </div>
        <Router hook={memoryLocationHook}>
          <Switch>
            <Route path="/paymentProcessing/*" component={PaymentProcessing} />
            <Route path="/atm" component={AtmPayments} />
            <Route path="/debit" component={DebitPayments} />
            <Route path="/credit" component={CreditPayments} />
            <Route path="/ach" component={AchPayments} />
            <Route path="/qr/*" component={QRCodeComponent} />
            <Route path="/summary" component={Checkout} />
            <Route component={VTPaymentOptions} />
          </Switch>
        </Router>
        <div className={style.poweredBy}>
          <div className={style.poweredByText}>Powered by </div>
          <img src={TPayIcon} />
        </div>
      </div>
    </div>
  );
};

export default VirtualTerminalMain;
