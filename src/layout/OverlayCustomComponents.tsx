import { h, Fragment } from 'preact';
import { useContext, useState } from 'preact/hooks';
import { toDollars } from '../utils/currencyUtils';
import warning from '../assets/warning.svg';
import quickGuide from '../assets/quick-guide.svg';
import danger from '../assets/danger.svg';
import TPayIcon from '../assets/treezpay.svg';
import success from '../assets/success.gif';
import loader from '../assets/achLoading.gif';
import infoIcon from '../assets/infoIcon.svg';
import FFDRotatingGradient from '../components/channels/ffd/rotatingGradient';
import VTRotatingGradient from '../components/channels/virtualTerminal/rotatingGradient';
import { ParsedErrorDetails } from '../utils/apiUtils';
import styles from './overlayComponents.css';
import treezPay from '../assets/treezPay.png';
import atmIcon from '../assets/atm.svg';
import clsx from 'clsx';
import { Channel } from '../models';
import Button from '../components/buttons/button';
import { ConfigContext, GlobalsContext } from '../AppContext';
import { sumAndFormat } from '../utils/money';
import CaptureCharge from '../components/channels/virtualTerminal/captureCharge';
import { PaymentNewSalesEvent } from '../events/PaymentEvents';
import useResetPayment from '../hooks/useResetPayment';

interface OverlayProps {
  paymentMethod?: string | null;
  providerName?: string | null;
  entityName?: string | null;
  totalAmount?: number;
  tipAmount?: number;
  feeAmount?: number;
  processorName?: string;
  onCancelHandler?: () => void;
  channel?: Channel;
  originalAmount?: number;
  cashbackAmount?: number;
  createdAt?: string;
}

interface OverlayErrorProps extends OverlayProps {
  errorMessage?: string;
  subErrorMessage?: string;
  primaryActionButton?: JSX.Element;
  secondaryActionButton?: JSX.Element;
  tertiaryActionButton?: JSX.Element;
}

interface VTCapturingFailedDetailedLayoutOverlayProps extends OverlayProps {
  paymentMethod: string;
  processorName: string;
  paymentChargeId?: string;
}

interface OverlayApiErrorProps extends OverlayProps {
  error: ParsedErrorDetails;
  secondaryActionButton?: JSX.Element;
}

export const PoweredBy = () => {
  return (
    <div className={styles.poweredBy}>
      <div className={styles.poweredByText}>Powered by </div>
      <img src={TPayIcon} />
    </div>
  );
};

export const VTPaymentLoader = ({
  header,
  subtext,
  showCancel = false,
  showWarningBanner = true,
}: {
  header?: string;
  subtext?: string;
  showCancel?: boolean;
  showWarningBanner?: boolean;
}) => {
  return (
    <Fragment>
      <div className={styles.vtContainer} id="tz-connecting-to-terminal">
        <span className={styles.centerAlign}>
          <img src={loader} alt="Loading" className={styles.vtLoader} />
        </span>
        <h1 className={styles.vtLoadingHeader}>
          {header || 'Processing payment'}
        </h1>
        <div className={styles.vtLoadingText}>
          {subtext || 'Checking device status...'}
        </div>
        {showCancel && (
          <Button
            variant="secondary"
            text="Cancel"
            onClick={() => {}}
            disabled={false}
            className={styles.cancelBtn}
          />
        )}
        {showWarningBanner && (
          <div className={styles.closeWindowWarning}>
            <div>Please ensure not to close the window</div>
            <div>
              Closing the window or navigating away from the page while
              electronic payment is in process may result in unrecorded payments
            </div>
          </div>
        )}
      </div>
      <PoweredBy />
    </Fragment>
  );
};

export const VTPaymentApprovedLayout = ({
  totalAmount,
  tipAmount,
  cashbackAmount,
  feeAmount,
  paymentMethod,
  createdAt,
}: OverlayProps) => {
  const {
    setWidgetOpen,
    setOverlayVisible,
    setOverlayContext,
    setShowTotalDetails,
    payment,
    paymentProcessor,
  } = useContext(GlobalsContext);

  const [showMoreDetails, setShowMoreDetails] = useState(false);

  const handleNewSale = () => {
    setWidgetOpen(false);
    setOverlayVisible(false);
    setOverlayContext(null);
    setShowTotalDetails(false);
    setShowMoreDetails(false);
    setWidgetOpen(false);
    const newSaleEvent: PaymentNewSalesEvent = {
      eventType: 'PAYMENT_NEW_SALES',
      userMessage: 'Start New Sales',
      data: {},
      processorName: payment?.processorName || '',
      paymentMethod: paymentProcessor?.paymentMethod || '',
    };
    payment?.handlePaymentResponse(newSaleEvent);
  };

  return (
    <Fragment>
      <div className={clsx(styles.vtContainer)} id="tz-payment-successful">
        <div className={styles.successIcon}>
          <img src={success} alt="Success Icon" />
        </div>
        <div className={styles.vtHeading}>Payment successful</div>
        <div className={styles.vtText}>
          Payment of{' '}
          <span className={styles.vtBoldText}>
            $
            {toDollars(
              (totalAmount || 0) + (cashbackAmount || 0) + (tipAmount || 0)
            )}
          </span>{' '}
          was successful
        </div>

        {paymentMethod === 'ATM' && (
          <div className={styles.vtChangeAmount}>
            <span>Change</span>
            <span>${toDollars(cashbackAmount)}</span>
          </div>
        )}

        <div className={styles.paymentSummary}>Payment summary</div>
        <div className={styles.paymentSummaryItems}>
          <div className={styles.summaryItem}>
            <span className={styles.summaryLabel}>Total</span>
            <span className={styles.summaryValue}>
              ${toDollars(totalAmount || 0)}
            </span>
          </div>
          <div className={styles.summaryItem}>
            <span className={styles.summaryLabel}>Tip</span>
            <span className={styles.summaryValue}>${toDollars(tipAmount)}</span>
          </div>
          <div className={styles.summaryItem}>
            <span className={styles.summaryLabel}>Total after tip</span>
            <span className={styles.summaryValue}>
              ${toDollars((totalAmount || 0) + (tipAmount || 0))}
            </span>
          </div>
        </div>
        {!showMoreDetails && (
          <Button
            className={styles.seeMoreButton}
            text="See more details"
            onClick={() => {
              setShowMoreDetails(true);
            }}
            variant="secondary"
            disabled={false}
          />
        )}

        {showMoreDetails && (
          <div className={styles.convenienceFeeContainer}>
            <div className={styles.convenienceFeeHeading}>
              <div className={styles.convenienceFeeLabel}>
                <span>Convenience fee</span>{' '}
                <img src={infoIcon} alt="Info Icon" />
              </div>
              <div className={styles.convenienceFeeValue}>
                ${toDollars(feeAmount || 0)}
              </div>
            </div>
            <div className={styles.subText}>(Not included in gross amount)</div>
            <hr className={styles.divider} />
            <div className={styles.totalWithFee}>
              <div className={styles.totalLabel}>Total (+ fee)</div>
              <div className={styles.totalValue}>
                $
                {sumAndFormat(
                  feeAmount,
                  totalAmount,
                  cashbackAmount,
                  tipAmount
                )}
              </div>
            </div>
          </div>
        )}

        <div className={styles.dateTime}>
          <span>
            {createdAt
              ? new Intl.DateTimeFormat('en-US', {
                  month: 'short',
                  day: '2-digit',
                  year: 'numeric',
                  hour: 'numeric',
                  minute: '2-digit',
                  hour12: true,
                }).format(new Date(createdAt))
              : ''}
          </span>
        </div>
        <Button
          className={styles.newSaleButton}
          text="Start New Sale"
          onClick={() => {
            handleNewSale();
          }}
          variant="primary"
          disabled={false}
        />
      </div>

      <PoweredBy />
    </Fragment>
  );
};

export const PaymentApprovedLayout = ({
  paymentMethod,
  providerName,
  totalAmount,
  tipAmount,
  feeAmount,
  entityName,
  channel,
  cashbackAmount,
  createdAt,
}: OverlayProps) => {
  return channel === Channel.VIRTUAL_TERMINAL ? (
    <VTPaymentApprovedLayout
      totalAmount={totalAmount}
      tipAmount={tipAmount}
      cashbackAmount={cashbackAmount}
      createdAt={createdAt}
      feeAmount={feeAmount}
      paymentMethod={paymentMethod}
      providerName={providerName}
      entityName={entityName}
    />
  ) : channel === Channel.ECOMMERCE ? null : (
    <Fragment>
      <div className={styles.container} id="tz-payment-successful">
        <div className={styles.successIcon}>
          <img src={success} alt="Success Icon" />
        </div>
        <h1 className={styles.heading}>Payment Successful!</h1>
        <p className={styles.text}>
          Your payment of{' '}
          <span className={styles.boldText}>
            ${toDollars((totalAmount || 0) + (feeAmount || 0))}
          </span>{' '}
          was successful.
        </p>
        <div className={styles.marginTop8em}>
          {
            <p>{`Thank you for shopping ${entityName ? `at ${entityName}` : `with us.`}`}</p>
          }
          <p className={styles.receiptText}>
            Don&apos;t forget to take your card and collect the bill receipt 😊
          </p>

          <div className={styles.qrContainer}>
            <img src={treezPay} alt="QR Code" />

            <div className={styles.textContainer}>
              <div className={styles.qrPaddingBottom}>
                Scan to learn more about cashless payment options
              </div>

              <div>
                Powered by <span className={styles.treezPayText}>TREEZPAY</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export const VTPaymentAuthorizedLayout = () => {
  return (
    <Fragment>
      <div className={styles.vtContainer} id="tz-payment-authorized">
        <div className={styles.successIcon}>
          <img src={success} alt="Success Icon" />
        </div>
        <h1 className={styles.vtAuthorizedPaymentHeader}>Payment authorized</h1>
      </div>

      <PoweredBy />
    </Fragment>
  );
};

export const PaymentCanceledLayout = ({
  paymentMethod,
  providerName,
  totalAmount,
}: OverlayProps) => {
  return (
    <Fragment>
      <div className={styles.container} id="tz-payment-canceled">
        <div className={styles.failureIcon}>
          <img src="/path/to/failure-icon.png" alt="Failure Icon" />
        </div>
        <h1 className={styles.headingCanceled}>Payment Canceled</h1>
        <p
          className={styles.text}
        >{`Your payment of $${toDollars(totalAmount)} via ${providerName} ${paymentMethod} was canceled.`}</p>
      </div>
    </Fragment>
  );
};

export const PaymentInProgressLayout = ({
  totalAmount,
  channel,
}: OverlayProps) => {
  return channel === Channel.VIRTUAL_TERMINAL ? (
    <VTPaymentLoader
      header="Processing payment"
      subtext="Waiting for device input"
    />
  ) : (
    <div className={styles.container} id="tz-payment-in-progress">
      <FFDRotatingGradient />
      <h1 className={styles.paymentInProgressHeader}>
        {`Pay `}
        <span className={styles.boldText}>{`$${toDollars(totalAmount)}`}</span>
      </h1>
      <p className={styles.text}>
        Insert, swipe or tap your card on the payment device
      </p>
      <div className={styles.paymentInProgressCancel}>
        To cancel this transaction - please use the payment device
      </div>
    </div>
  );
};

export const GeneratingQrCodeLayout = ({
  totalAmount,
  channel,
}: OverlayProps) => {
  return channel === Channel.VIRTUAL_TERMINAL ? (
    <VTPaymentLoader
      header="Generating QR code"
      subtext="Hold tight! Almost there..."
      showCancel={false}
      showWarningBanner={false}
    />
  ) : (
    <Fragment>
      <div className={styles.container} id="tz-generating-qr-code">
        <div className={styles.successIcon}>
          <img src={loader} alt="Success Icon" />
        </div>
        <p className={styles.generatingQrCodeHeader}>
          Generating your magical QR code
        </p>
        <p className={styles.generatingQrCodeText}>
          Hold tight! Almost there...
        </p>
      </div>
    </Fragment>
  );
};

export const VTCapturingInProgressLayout = () => {
  return (
    <Fragment>
      <div
        className={styles.vtContainer}
        id="tz-virtual-terminal-ach-capturing"
      >
        <VTRotatingGradient />
        <div className={styles.vtCapturingHeader}>
          Capturing payment, hold on...
        </div>
        <div className={styles.vtCapturingText}>
          ACH payments are required to be captured manually after
        </div>
        <div className={styles.vtCapturingText}>
          being authorized. Please wait while we capture.
        </div>
      </div>

      <PoweredBy />
    </Fragment>
  );
};

export const VTCapturingSuccessLayout = ({
  totalAmount,
  cashbackAmount,
  tipAmount,
}: OverlayProps) => {
  return (
    <Fragment>
      <div
        className={styles.vtContainer}
        id="tz-virtual-terminal-ach-capturing-success"
      >
        <div className={styles.successIcon}>
          <img src={success} alt="Success Icon" />
        </div>
        <h1 className={styles.capturingSuccessHeader}>Payment successful</h1>
        <div className={styles.vtText}>
          Payment of{' '}
          <span className={styles.boldText}>
            $
            {toDollars(
              (totalAmount || 0) + (cashbackAmount || 0) + (tipAmount || 0)
            )}
          </span>{' '}
          was successful
        </div>
      </div>

      <PoweredBy />
    </Fragment>
  );
};

export const VTCapturingFailedLayout = () => {
  return (
    <Fragment>
      <div
        className={styles.vtContainer}
        id="tz-virtual-terminal-ach-capturing-failed"
      >
        <div>
          <img src={danger} alt="Failure Icon" />
        </div>
        <div className={styles.vtCapturingHeader}>Payment capture failed</div>
      </div>

      <PoweredBy />
    </Fragment>
  );
};

export const VTCapturingFailedDetailedLayout = ({
  paymentMethod,
  processorName,
  paymentChargeId,
}: VTCapturingFailedDetailedLayoutOverlayProps) => {
  const { navigate, setOverlayContext, setOverlayVisible, setApiError } =
    useContext(GlobalsContext);

  const resetPayment = useResetPayment();

  return (
    <Fragment>
      <div
        className={clsx(styles.container, styles.animateOverlay)}
        id="tz-virtual-terminal-ach-capturing-failed-detailed"
      >
        <div>
          <img src={danger} alt="Failure Icon" />
        </div>
        <div className={styles.vtCapturingFailedHeader}>
          Payment capture failed
        </div>
        <div className={styles.vtCapturingText}>
          We were unable to capture the ACH transaction.
        </div>
        <div className={styles.vtCapturingFailedQuickGuideContainer}>
          <div className={styles.vtCapturingFailedQuickGuideHeaderContainer}>
            <img src={quickGuide} alt="Quick Guide" />
            <div className={styles.vtCapturingFailedQuickGuideText}>
              Quick Guide
            </div>
          </div>
          <ul className={styles.vtCapturingFailedQuickGuideList}>
            <li>
              <span>Try capturing the ACH transaction again</span>
            </li>
            <li>
              <span>
                If the issue persists, void this transaction to switch to
                another payment option and complete the sale.
              </span>
            </li>
          </ul>
        </div>
        <Button
          className={styles.retryCaptureButton}
          text="Retry capture"
          onClick={() => {
            setOverlayContext({
              customComponent: (
                <CaptureCharge
                  paymentMethod={paymentMethod}
                  processorName={processorName}
                  paymentChargeId={paymentChargeId}
                />
              ),
              disableButtonsOnProgress: true,
            });
          }}
          variant="primary"
          disabled={false}
        />
        <Button
          className={styles.voidAndChangeButton}
          text="Void and change payment method"
          onClick={() => {
            setOverlayVisible(false);
            setOverlayContext(null);
            setApiError(null);
            resetPayment();
            navigate('/');
          }}
          variant="secondary"
          disabled={false}
        />
      </div>

      <PoweredBy />
    </Fragment>
  );
};

export const ACHPaymentPreparingLayout = () => {
  return (
    <Fragment>
      <div className={styles.vtContainer} id="tz-ach-payment-preparing">
        <VTRotatingGradient />
        <div className={styles.achPaymentPreparingHeader}>Almost there...</div>
        <div className={styles.achPaymentPreparingText}>
          Ensure the customer remains until payment is complete
        </div>
      </div>

      <PoweredBy />
    </Fragment>
  );
};

export const GeneratingInvoiceLayout = ({ totalAmount }: OverlayProps) => {
  return (
    <div className={styles.container} id="tz-generating-qr-code">
      <div className={styles.successIcon}>
        <img src={loader} alt="Success Icon" />
      </div>
      <p className={styles.generatingQrCodeHeader}>
        Generating your magical QR code
      </p>
      <p className={styles.generatingQrCodeText}>Hold tight! Almost there...</p>
    </div>
  );
};

export const PaymentPreparingLayout = ({ channel }: { channel?: Channel }) => {
  return channel === Channel.VIRTUAL_TERMINAL ? (
    <VTPaymentLoader />
  ) : (
    <Fragment>
      <div className={styles.container} id="tz-connecting-to-terminal">
        <img src={atmIcon} className={styles.atmIcon} width={99} height={239} />
        <h1 className={styles.connectingToTerminalHeader}>
          Connecting to payment device...
        </h1>
      </div>
    </Fragment>
  );
};

export const PaymentCancelationProgressLayout = ({
  totalAmount,
}: OverlayProps) => {
  return (
    <div className={styles.container} id="tz-canceling-payment">
      <FFDRotatingGradient />
      <h1
        className={styles.heading}
      >{`Canceling payment of $${toDollars(totalAmount)} `}</h1>
      <p className={styles.text}>This will take a few seconds...</p>
    </div>
  );
};

export const PaymentErrorLayout = ({
  errorMessage,
  subErrorMessage,
  primaryActionButton,
  secondaryActionButton,
  tertiaryActionButton,
  channel,
}: OverlayErrorProps) => {
  if (channel === Channel.ECOMMERCE) return null;
  return (
    <Fragment>
      <div className={clsx(styles.container)} id="tz-payment-error">
        <div>
          <img src={warning} alt="Failure Icon" />
        </div>
        <div>
          {errorMessage && (
            <h1
              className={
                subErrorMessage ? styles.headingCanceled : styles.marginBottom
              }
            >
              {errorMessage}
            </h1>
          )}
          {subErrorMessage && (
            <p className={clsx(styles.text, styles.marginBottom)}>
              {subErrorMessage}
            </p>
          )}
          {primaryActionButton && (
            <div className={clsx(styles.errorContainer, styles.gapBottom)}>
              {primaryActionButton}
            </div>
          )}
          {secondaryActionButton && (
            <div className={clsx(styles.errorContainer, styles.gapBottom)}>
              {secondaryActionButton}
            </div>
          )}
          {tertiaryActionButton && tertiaryActionButton}
        </div>
      </div>
    </Fragment>
  );
};

export const AppLoadingLayout = () => {
  const config = useContext(ConfigContext);

  return (
    <Fragment>
      <div className={styles.container} id="tz-app-loading">
        <img
          src={loader}
          alt="Loading"
          className={
            config.channel === Channel.VIRTUAL_TERMINAL && styles.loadingGif
          }
        />
      </div>
    </Fragment>
  );
};

export const ApiErrorLayout = ({
  error,
  secondaryActionButton,
}: OverlayApiErrorProps) => {
  return (
    <Fragment>
      <div className={clsx(styles.container)} id="tz-api-error">
        <div>
          <img src={warning} alt="Failure Icon" />
        </div>
        <div>
          {error.message && (
            <h1 className={styles.headingCanceled}>{error.message}</h1>
          )}
          <div className={styles.marginBottom}>
            {error.method && error.endpoint && error.statusCode && (
              <p className={styles.text}>
                {`The request for ${error.method} ${error.endpoint} failed with status code ${error.statusCode}.`}
              </p>
            )}
          </div>
          {secondaryActionButton && (
            <div className={clsx(styles.errorContainer, styles.marginBottom)}>
              {secondaryActionButton}
            </div>
          )}
        </div>
      </div>
    </Fragment>
  );
};
