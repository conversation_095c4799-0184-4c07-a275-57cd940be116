import { Fragment, h } from 'preact';
import { useContext, useEffect } from 'preact/hooks';
import { ConfigContext, GlobalsContext } from '../AppContext';
import { FunctionalComponent } from 'preact/src';
import { Channel } from '../models';
import FFDMain from './FFDMain';
import VirtualTerminalMain from './VirtualTerminalMain';
import ECommerceMain from './ECommerceMain';

const Main: FunctionalComponent = () => {
  const config = useContext(ConfigContext);
  const { channel } = config;
  const { widgetOpen } = useContext(GlobalsContext);

  if (!widgetOpen) return <Fragment />;

  useEffect(() => {
    console.log(`Initializing ${channel} channel`);
  }, [channel]);

  switch (channel) {
    case Channel.VIRTUAL_TERMINAL:
      return <VirtualTerminalMain />;
    case Channel.ECOMMERCE:
      return <ECommerceMain />;
    case Channel.FFD:
    default:
      return <FFDMain />;
  }
};

export default Main;
