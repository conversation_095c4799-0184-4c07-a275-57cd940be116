import { h } from 'preact';

import { Route, Switch, Router } from 'wouter-preact';
import globalStyles from './styles/global.css';
import style from './styles/ffd.css';
import TreezPay from '../routes/TreezPay';
import { useContext } from 'preact/hooks';
import { ConfigContext, GlobalsContext } from '../AppContext';
import clsx from 'clsx';
import TitleBar from '../components/TitleBar';
import TotalDisplay from '../components/totalDisplay';
import AtmPayments from '../routes/AtmPayments';
import AchPayments from '../routes/AchPayments';
import TipPayments from '../routes/TipPayments';
import Overlay from '../components/Overlay';
import DebitPayments from '../routes/DebitPayments';
import { useTheme } from '../context/ThemeProvider';
import AsyncResponseEventHandler from '../routes/AsyncResponseEventHandler';
import CustomProcessorPaymentHandler from '../components/handlers/CustomProcessorPaymentHandler';
import { FunctionalComponent } from 'preact/src';
import QRCodeComponent from '../components/QRCode';
import PaymentProcessing from '../routes/PaymentProcessing';
import Checkout from '../routes/Checkout';
import ShowBackButton from '../components/backbutton/ShowBackButton';
import AppLoading from '../components/AppLoading';
import { DeviceContext } from '../context/DeviceProvider';
import ApiError from '../components/ApiError';
import { useWebSocket } from '../context/WebSocketProvider';
import CreditPayments from '../routes/CreditPayments';

const FFDMain: FunctionalComponent = () => {
  const config = useContext(ConfigContext);
  const { isWebSocketEnabled } = config;
  const deviceContext = useContext(DeviceContext);
  const { isConnected } = useWebSocket();
  const { theme } = useTheme();
  const {
    overlayVisible,
    setOverlayVisible,
    overlayMessage,
    overlayContext,
    setOverlayContext,
    widgetOpen,
    memoryLocationHook,
    apiError,
    currentPath,
    setShowTotalDetails,
  } = useContext(GlobalsContext);
  const showTotalDisplay = !(
    ['/qr', '/ecommerce', '/summary', '/paymentProcessing/'].some(path =>
      currentPath.startsWith(path)
    ) ||
    deviceContext?.loading ||
    apiError
  );

  return (
    <div
      className={clsx(
        globalStyles.reset,
        style.root,
        { [style.noDark]: config.disableDarkMode },
        { [style.rootFixed]: theme.positionFixed },
        { [style.rootMinimised]: !widgetOpen },
        theme.positionFixed ? style.rootEmbed : style.rootFullScreen
      )}
    >
      <TitleBar />
      {(deviceContext?.loading || (isWebSocketEnabled && !isConnected)) && (
        <AppLoading />
      )}
      {apiError && <ApiError />}
      {overlayVisible && (
        <Overlay
          context={overlayContext}
          message={overlayMessage}
          onCancel={() => {
            setOverlayVisible(false);
            setOverlayContext(null);
            setShowTotalDetails(false);
          }}
        />
      )}
      {showTotalDisplay && <TotalDisplay />}
      <AsyncResponseEventHandler />
      <CustomProcessorPaymentHandler />
      <ShowBackButton />
      <div
        className={clsx(
          style.container,
          { [style.minimized]: !widgetOpen },
          { [style.showTotalDisplay]: showTotalDisplay },
          config.styles.classNameContainer
        )}
      >
        <Router hook={memoryLocationHook}>
          <Switch>
            <Route path="/summary" component={Checkout} />
            <Route path="/paymentProcessing/*" component={PaymentProcessing} />
            <Route path="/atm" component={AtmPayments} />
            <Route path="/debit" component={DebitPayments} />
            <Route path="/credit" component={CreditPayments} />
            <Route path="/ach" component={AchPayments} />
            <Route path="/tips" component={TipPayments} />
            <Route path="/qr/*" component={QRCodeComponent} />
            <Route component={TreezPay} />
          </Switch>
        </Router>
      </div>
    </div>
  );
};

export default FFDMain;
