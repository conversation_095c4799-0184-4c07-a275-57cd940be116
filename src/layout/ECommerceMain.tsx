import { h } from 'preact';
import { useContext, useEffect } from 'preact/hooks';
import clsx from 'clsx';
import { FunctionalComponent } from 'preact/src';
import AsyncResponseEventHandler from '../routes/AsyncResponseEventHandler';
import { DeviceContext } from '../context/DeviceProvider';
import AccordionGroup from '../components/accordion';
import { Accordion } from '../components/accordion';
import globalStyles from './styles/global.css';
import style from './styles/ecommerce.css';
import PaymentMethod from '../components/channels/ecommerce/PaymentMethod';
import AchPayments from '../routes/AchPayments';
import CreditPayments from '../components/channels/ecommerce/CreditPayment';
import { ConfigContext, GlobalsContext } from '../AppContext';
import Overlay from '../components/Overlay';


const ECommerceMain: FunctionalComponent = () => {
  const deviceContext = useContext(DeviceContext);
  const { overlayContext, overlayVisible } = useContext(GlobalsContext);
  const config = useContext(ConfigContext);
  const shouldShowOverlay =
    !config?.onPaymentLoading &&
    overlayVisible &&
    overlayContext &&
    'customComponent' in overlayContext;

  const accordionItems = Object.entries(
    deviceContext?.paymentMethods || {}
  ).map(([key]) => {
    return {
      title: (isOpen: boolean) => (
        <PaymentMethod method={key} selected={isOpen} />
      ),
      onExpand: key === 'ACH' ? <AchPayments /> : <CreditPayments />,
      key,
    };
  });

  useEffect(() => {
    // render configured loader
    if (config?.onPaymentLoading) {
      config.onPaymentLoading?.(overlayVisible);
    }
  }, [config?.onPaymentLoading, overlayVisible]);

  return (
    <div className={clsx(globalStyles.reset, style.root)}>
      {accordionItems?.length === 1 ? (
        <Accordion
          title={() => (
            <PaymentMethod method={accordionItems[0].key} selected={true} />
          )}
          onExpand={accordionItems[0].onExpand}
          isOpen
          nonCollapsible
        />
      ) : (
        <AccordionGroup items={accordionItems} />
      )}
      <AsyncResponseEventHandler />
      {shouldShowOverlay && <Overlay context={overlayContext} />}
    </div>
  );
};

export default ECommerceMain;
