import { h, createContext } from 'preact';
import { useContext, useState } from 'preact/hooks';
import { ComponentChildren } from 'preact';
import { ConfigContext } from '../AppContext';

export interface Theme {
  positionFixed: boolean;
}

interface ThemeContextProps {
  theme: Theme;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextProps>({
  theme: { positionFixed: false},
  setTheme: () => {},
});

// Encapsulate the useTheme hook for easier consumption
export const useTheme = () => useContext(ThemeContext);

interface ThemeProviderProps {
  children: ComponentChildren;
}

export const ThemeProvider = ({ children }: ThemeProviderProps) => {
  // Get the theme object from ConfigContext
  const config = useContext(ConfigContext);
  const themeObject = config.theme || { positionFixed: false };

  const [theme, setTheme] = useState<Theme>(themeObject);

  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
