import { ComponentChildren, createContext, FunctionComponent, h } from 'preact';
import { useContext, useState, useCallback } from 'preact/hooks';
import { GlobalsContext, ServiceContext } from '../AppContext';
import { Preview } from '../models';
import useLogger from '../hooks/useLogger';

export interface PreviewContextProps {
  previewTotals: Preview | null;
  loading: boolean;
  fetchPreviewPaymentTotals: (
    processorName: string,
    tipAmount?: number
  ) => void;
  reset: () => void;
}

export const PreviewContext = createContext<PreviewContextProps | null>(null);

interface PreviewProviderProps {
  children: ComponentChildren;
  previewTotals: Preview | null;
  setPreviewTotals: (preview: Preview | null) => void;
}

export const PreviewProvider: FunctionComponent<PreviewProviderProps> = ({
  children,
  previewTotals,
  setPreviewTotals,
}) => {
  const logger = useLogger();

  const service = useContext(ServiceContext);
  const { payment } = useContext(GlobalsContext);

  const [loading, setLoading] = useState(false);

  const fetchPreviewPaymentTotals = useCallback(
    async (processorName: string, tip?: number) => {
      if (!service) {
        logger.error('Service is undefined');
        return;
      }

      setLoading(true);

      const tipAmount = tip != undefined ? tip : payment?.tip || 0;
      try {
        const previewTotalsRes = await service.getPreview({
          processorName: processorName,
          totalAmount: payment?.originalAmount || 0,
          tipAmount,
        });

        if (previewTotalsRes?.data) {
          setPreviewTotals(previewTotalsRes.data as Preview);
        }
      } catch (error) {
        logger.error('Error fetching preview:', error);
        setPreviewTotals(null);
      } finally {
        setLoading(false);
      }
    },
    [service, payment, setPreviewTotals]
  );

  const reset = () => {
    setPreviewTotals(null);
  };

  return (
    <PreviewContext.Provider
      value={{
        previewTotals,
        loading,
        fetchPreviewPaymentTotals,
        reset,
      }}
    >
      {children}
    </PreviewContext.Provider>
  );
};
