import { createContext, h } from 'preact';
import { ReactNode, useContext } from 'preact/compat';
import { GlobalsContext } from '../AppContext';
import useLogger from '../hooks/useLogger';

interface EventDispatcherContextType {
  dispatchEvent: (eventName: string, detail?: any) => void;
}

const EventDispatcherContext = createContext<
  EventDispatcherContextType | undefined
>(undefined);

export const EventDispatcherProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const logger = useLogger();

  const { element } = useContext(GlobalsContext);

  const dispatchEvent = (eventName: string, detail?: any) => {
    if (element) {
      const event = new CustomEvent('widget-event', {
        detail: { name: eventName, detail },
      });
      element.dispatchEvent(event); // Dispatch the event on the provided element
    } else {
      logger.warn('No element found in GlobalsContext to dispatch event.');
    }
  };

  return (
    <EventDispatcherContext.Provider value={{ dispatchEvent }}>
      {children}
    </EventDispatcherContext.Provider>
  );
};

// Hook to use the EventDispatcher context
export const useEventDispatcher = () => {
  const context = useContext(EventDispatcherContext);
  if (!context) {
    throw new Error(
      'useEventDispatcher must be used within EventDispatcherProvider'
    );
  }
  return context;
};
