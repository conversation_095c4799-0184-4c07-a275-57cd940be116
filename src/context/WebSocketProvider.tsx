import { useContext, useEffect, useRef, useState } from 'preact/hooks';
import { h, createContext } from 'preact';

import { ParsedErrorDetails } from '../utils/apiUtils';
import useLogger from '../hooks/useLogger';
import { ConfigContext, GlobalsContext } from '../AppContext';
import useDetectRootRemoval from '../hooks/useDetectRootRemoval';

type EventCallback = (data: any) => void;

interface WebSocketContextType {
  isConnected: boolean;
  sendMessage: (message: string) => void;
  addMessageListener: (callback: EventCallback) => void;
  removeMessageListener: (callback: EventCallback) => void;
  addErrorListener: (callback: EventCallback) => void;
  removeErrorListener: (callback: EventCallback) => void;
  close: () => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(
  undefined
);

interface WebSocketWithSession extends WebSocket {
  sessionId?: string;
}

export const WebSocketProvider = ({
  authTokenFactory,
  sessionId,
  reconnectInterval = 1000,
  setApiError,
  children,
}: {
  authTokenFactory: () => Promise<string | undefined>;
  sessionId: string;
  reconnectInterval?: number;
  setApiError: (error: ParsedErrorDetails) => void;
  children: preact.ComponentChildren;
}) => {
  const logger = useLogger();
  const { isWebSocketEnabled, autoRenderPayments, consumerType } = useContext(ConfigContext);
  const [isConnected, setIsConnected] = useState(false);
  const ws = useRef<WebSocketWithSession | null>(null);

  const messageListeners = useRef<EventCallback[]>([]);
  const errorListeners = useRef<EventCallback[]>([]);

  const reconnectAttempts = useRef(0);
  const reconnectTimeout = useRef<NodeJS.Timeout | null>(null);
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);
  const lastPingTime = useRef<Date | null>(null);
  const websocketError = useRef<ParsedErrorDetails | null>({
    message: 'An unknown WebSocket error occurred',
  });
  const shouldReconnect = useRef(true);

  const MAX_RECONNECT_DELAY = 30000; // Maximum backoff time (30 seconds)

  const { element } = useContext(GlobalsContext);

  const checkConnectionInterval = useRef<NodeJS.Timeout | null>(null);

  if (element) {
    useDetectRootRemoval(element, () => {
      logger.log(
        `Root element removed. Closing websocket for session ${ws.current?.sessionId}`
      );
      shouldReconnect.current = false;
      close();
    });
  }

  const scheduleReconnect = () => {
    if (reconnectTimeout.current) {
      clearTimeout(reconnectTimeout.current);
    }

    // Debounce logic: Clear existing timeout if reconnection is scheduled quickly
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    if (reconnectAttempts.current >= 7) {
      if (websocketError.current) {
        setApiError(websocketError.current);
        return;
      }
    }

    // Exponential backoff: Increase delay with each failed attempt, up to the max delay
    const delay = Math.min(
      reconnectInterval * 2 ** reconnectAttempts.current,
      MAX_RECONNECT_DELAY
    );

    debounceTimeout.current = setTimeout(() => {
      reconnectAttempts.current += 1;
      logger.log(
        `Reconnecting... Attempt #${reconnectAttempts.current}, waiting ${delay}ms`
      );
      connect();
    }, delay);
  };

  const resetReconnectAttempts = () => {
    reconnectAttempts.current = 0;
    if (reconnectTimeout.current) {
      clearTimeout(reconnectTimeout.current);
      reconnectTimeout.current = null;
    }
  };

  const connect = () => {
    shouldReconnect.current = true; // enable reconnect when opening fresh connection

    if (ws.current) {
      ws.current.close();
    }
    const websocket = new WebSocket(
      `${process.env.API_GATEWAY_WEBSOCKET_ENDPOINT}`
    );

    websocket.onopen = async () => {
      logger.log('WebSocket connection opened');

      const token = (await authTokenFactory()) || '';

      if (token) {
        const authMessage = JSON.stringify({
          sessionId,
          action: 'authenticate',
          token: token,
          type: consumerType,
        });
        websocket.send(authMessage);
      }

      resetReconnectAttempts();

      lastPingTime.current = new Date(); // Initialize ping tracking when connected
    };

    websocket.onmessage = event => {
      logger.info('WebSocket onmessage:', event.data);
      const data = JSON.parse(event.data);
      if (data.statusCode === 200) {
        setIsConnected(true);
      } else if (data.statusCode === 500) {
        scheduleReconnect();
      }
      if (data.type === 'ping') {
        lastPingTime.current = new Date(); // Update the last received ping time
        logger.log('Received ping, updating lastPingTime');
      }

      notifyListeners(messageListeners.current, JSON.parse(event.data));
    };

    websocket.onerror = error => {
      logger.error('WebSocket error:', error);
      setIsConnected(false);
      notifyListeners(errorListeners.current, error);
      websocketError.current = {
        message: 'An unknown WebSocket error occurred',
      };
      scheduleReconnect();
    };

    websocket.onclose = event => {
      logger.error(`WebSocket closed: ${event.code}, reason: ${event.reason}`);
      websocketError.current = {
        message: `WebSocket closed with code: ${event.code}`,
      };
      setIsConnected(false);
      if (shouldReconnect.current) {
        scheduleReconnect();
      } else {
        logger.log('Websocket reconnect skipped: widget unmounted');
      }
    };

    ws.current = websocket;
    ws.current.sessionId = sessionId;
  };

  const notifyListeners = (listeners: EventCallback[], data: any) => {
    listeners.forEach(callback => callback(data));
  };

  const sendMessage = (message: string) => {
    if (isConnected && ws?.current) {
      ws.current?.send(message);
    } else {
      logger.warn('WebSocket is not connected. Message not sent.');
    }
  };

  const close = () => {
    if (ws?.current) {
      ws.current?.close();
      ws.current = null;
    }
    if (reconnectTimeout.current) {
      clearTimeout(reconnectTimeout.current);
      reconnectTimeout.current = null;
    }
    if (checkConnectionInterval.current) {
      clearInterval(checkConnectionInterval.current);
      checkConnectionInterval.current = null;
    }
  };

  const addMessageListener = (callback: EventCallback) => {
    messageListeners.current.push(callback);
  };

  const removeMessageListener = (callback: EventCallback) => {
    const index = messageListeners.current.indexOf(callback);
    if (index !== -1) messageListeners.current.splice(index, 1);
  };

  const addErrorListener = (callback: EventCallback) => {
    errorListeners.current.push(callback);
  };

  const removeErrorListener = (callback: EventCallback) => {
    const index = errorListeners.current.indexOf(callback);
    if (index !== -1) errorListeners.current.splice(index, 1);
  };

  useEffect(() => {
    if (isWebSocketEnabled && autoRenderPayments) {
      connect();
    }

    return () => {
      close();
    };
  }, []); // ensures this runs only once

  // Monitor for stale connections (missing pings)
  useEffect(() => {
    checkConnectionInterval.current = setInterval(() => {
      if (lastPingTime.current) {
        const now = new Date();
        const timeSinceLastPing =
          now.getTime() - lastPingTime.current.getTime();

        const threshold = 240000 + 60000; // 4 minutes + 1 minute buffer = 300,000 milliseconds

        if (timeSinceLastPing > threshold) {
          logger.warn(
            'No ping received in the last 5 minutes (including buffer), reconnecting...'
          );
          setIsConnected(false);
          close();
          scheduleReconnect();
        }
      }
    }, 60000); // Check every 1 minute

  }, []); // Empty dependency ensures this only runs once

  return (
    <WebSocketContext.Provider
      value={{
        isConnected,
        sendMessage,
        addMessageListener,
        removeMessageListener,
        addErrorListener,
        removeErrorListener,
        close,
      }}
    >
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};
