import { ComponentChildren, createContext, FunctionComponent, h } from 'preact';
import {
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
} from 'preact/hooks';
import { ConfigContext, GlobalsContext, ServiceContext } from '../AppContext';
import {
  Channel,
  Configuration,
  PaymentDeviceLocation,
  Processor,
} from '../models';
import {
  createPaymentMethodConfigMapping,
  getECommerceEnabledProcessors,
  groupProcessorFlowsByPaymentMethod,
  groupProcessorsByPaymentMethod,
  PaymentMethodFeeMapping,
  PaymentMethodRoundingMapping,
  PaymentMethods,
} from '../utils/paymentDeviceUtils';
import useLogger from '../hooks/useLogger';
import { IProcessorFlowConfigByMethod } from '../types/processor';

type PaymentMethodDeviceLocations = { [key: string]: PaymentDeviceLocation[] };
export type ProcessorConfigurations = { [key: string]: Configuration[] };

export interface DeviceContextProps {
  paymentMethods: PaymentMethods;
  paymentMethodByDeviceLocation: PaymentMethodDeviceLocations;
  paymentMethodFeeMapping: PaymentMethodFeeMapping;
  processorFlowConfigByMethod: IProcessorFlowConfigByMethod;
  isPreTippingEnabled: (processorName: string) => Boolean;
  isInvoicePaymentEnabled: (processorName: string) => Boolean;
  getPaymentMethodFee: (paymentMethodName: string) => number;
  getPaymentMethodRoundingAmount: (paymentMethodName: string) => string;
  getPaymentMethodByProcessorId: (processorId: string) => string | undefined;
  getProcessorNameByProcessorId: (processorId: string) => string | undefined;
  getPaymentMethodByProcessorName: (
    processorName: string
  ) => string | undefined;
  loading: boolean;
  refetch: () => void;
  getProcessorNameFeeMapping: (
    paymentMethodName: string
  ) => Record<string, string | undefined>;
  getTippingEnabledPercentValues: (processorName: string) => number[];
}

export enum ProcessorConfigCodes {
  PROCESSOR_FEE = 'processorFee',
  PROCESSOR_ROUND_TO_VALUE = 'processorRoundToValue',
  IS_PRE_TIPPING_ENABLED = 'ffdTippingEnabled',
  IS_CHECKOUT_ENABLED = 'isCheckoutEnabled',
  IN_STORE_ACH_ENABLED = 'inStoreAchEnabled',
  IS_INVOICE_PAYMENT_ENABLED = 'isInvoicePaymentEnabled',
  TIPPING_ENABLED_PERCENT_VALUES = 'tippingEnabledPercentValues',
}

// Create the context
export const DeviceContext = createContext<DeviceContextProps | null>(null);

interface DeviceProviderProps {
  children: ComponentChildren;
}

// Device Provider
export const DeviceProvider: FunctionComponent<DeviceProviderProps> = ({
  children,
}) => {
  const logger = useLogger();

  const service = useContext(ServiceContext);
  const { payment, setApiError } = useContext(GlobalsContext);
  const { channel, onReady } = useContext(ConfigContext);

  const [processors, setProcessors] = useState<Processor[]>([]);
  const [allProcessorsByPaymentMethods, setAllProcessorsByPaymentMethods] =
    useState<PaymentMethods>({});
  const [
    defaultProcessorsByPaymentMethods,
    setDefaultProcessorsByPaymentMethods,
  ] = useState<PaymentMethods>({});
  const [paymentMethodByDeviceLocations, setPaymentMethodByDeviceLocation] =
    useState<PaymentDeviceLocation[]>([]);
  const [paymentMethodFeeMapping, setPaymentMethodFeeMapping] =
    useState<PaymentMethodFeeMapping>({});
  const [paymentMethodRoundingMapping, setPaymentMethodRoundingMapping] =
    useState<PaymentMethodRoundingMapping>({});
  const [processorConfigurations, setProcessorConfigurations] =
    useState<ProcessorConfigurations>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [processorFlowConfigByMethod, setProcessorFlowConfigByMethod] =
    useState<IProcessorFlowConfigByMethod>({} as IProcessorFlowConfigByMethod);

  const isECommerceChannel = channel === Channel.ECOMMERCE;

  const isPreTippingEnabled = (processorName: string): Boolean => {
    if (channel === Channel.VIRTUAL_TERMINAL) return false;
    if (!processorConfigurations[processorName]) {
      return false;
    }
    return processorConfigurations[processorName].some(
      ({ code, value }) =>
        code === ProcessorConfigCodes.IS_PRE_TIPPING_ENABLED && value === 'true'
    );
  };

  const isInvoicePaymentEnabled = (processorName: string): Boolean => {
    if (!processorConfigurations[processorName]) {
      return false;
    }
    return processorConfigurations[processorName].some(
      ({ code, value }) =>
        code === ProcessorConfigCodes.IS_INVOICE_PAYMENT_ENABLED &&
        value === 'true'
    );
  };

  const getTippingEnabledPercentValues = (processorName: string): number[] => {
    if (!isPreTippingEnabled(processorName)) return [];

    const tippingPercentValues = processorConfigurations[processorName].find(
      ({ code }) => code === ProcessorConfigCodes.TIPPING_ENABLED_PERCENT_VALUES
    )?.value;

    if (!tippingPercentValues) return [5, 10, 15];

    return tippingPercentValues?.split(',').map(Number) || [];
  };

  const getPaymentMethodFee = (paymentMethodName: string): number => {
    const defaultProcessor =
      defaultProcessorsByPaymentMethods[paymentMethodName];

    if (!defaultProcessor || defaultProcessor.length === 0) {
      logger.debug(
        `No default processor found for payment method: ${paymentMethodName}`
      );
      return 0;
    }

    const fee = paymentMethodFeeMapping[defaultProcessor[0]];

    if (!fee) {
      logger.debug(
        `No fee mapping found for method: ${paymentMethodName}, processor: ${defaultProcessor[0]}`
      );
      return 0;
    }

    return Number(fee);
  };

  const getProcessorNameFeeMapping = (
    paymentMethod: string
  ): Record<string, string | undefined> => {
    const achProcessors = allProcessorsByPaymentMethods[paymentMethod] || [];

    return achProcessors.reduce<Record<string, string | undefined>>(
      (acc, processorName) => {
        acc[processorName] = paymentMethodFeeMapping[processorName];
        return acc;
      },
      {}
    );
  };

  const getPaymentMethodRoundingAmount = (
    paymentMethodName: string
  ): string => {
    const defaultProcessor =
      defaultProcessorsByPaymentMethods[paymentMethodName];

    if (!defaultProcessor || defaultProcessor.length === 0) {
      logger.debug(
        `No default processor found for payment method: ${paymentMethodName}`
      );
      return '';
    }

    const roundingAmount = paymentMethodRoundingMapping[defaultProcessor[0]];

    if (!roundingAmount) {
      logger.debug(
        `No fee mapping found for method: ${paymentMethodName}, processor: ${defaultProcessor[0]}`
      );
      return '';
    }

    return roundingAmount;
  };

  const getPaymentMethodByProcessorId = (processorId: string) => {
    const processor = processors.find(
      processor => processor.processorId === processorId
    );
    return processor?.paymentMethod;
  };

  const getPaymentMethodByProcessorName = (processorName: string) => {
    const processor = processors.find(
      processor => processor.processorName === processorName
    );
    return processor?.paymentMethod;
  };

  const getProcessorNameByProcessorId = (processorId: string) => {
    const processor = processors.find(
      processor => processor.processorId === processorId
    );
    return processor?.processorName;
  };

  const fetchProcessors = useCallback(async () => {
    if (!service) {
      logger.error('Service is undefined');
      return;
    }

    setApiError(null);
    setLoading(true);

    try {
      const processorsResult = await service.getProcessors(channel || Channel.FFD);
      let processors: Processor[] = processorsResult.data?.processors;

      if (isECommerceChannel)
        processors = getECommerceEnabledProcessors(processors);

      setProcessors(processors);
      setProcessorFlowConfigByMethod(groupProcessorFlowsByPaymentMethod(processors));

      const defaultProcessorsByPaymentMethods = groupProcessorsByPaymentMethod(
        processors,
        true
      );
      setDefaultProcessorsByPaymentMethods(defaultProcessorsByPaymentMethods);

      const allProcessorsByPaymentMethods =
        groupProcessorsByPaymentMethod(processors);
      setAllProcessorsByPaymentMethods(allProcessorsByPaymentMethods);

      const integratedProviderNames = Object.keys(allProcessorsByPaymentMethods)
        .filter(method => method !== 'ACH') // Exclude ACH if needed
        .map(method => allProcessorsByPaymentMethods[method])
        .reduce<string[]>((acc, value) => acc.concat(value), []);

      if (
        integratedProviderNames &&
        integratedProviderNames.length !== 0 &&
        !isECommerceChannel
      ) {
        const paymentDeviceLocationsResult =
          await service.getPaymentDeviceLocations(integratedProviderNames);
        const paymentDeviceLocations: PaymentDeviceLocation[] =
          paymentDeviceLocationsResult?.data || [];

        setPaymentMethodByDeviceLocation(paymentDeviceLocations);
      }

      setPaymentMethodFeeMapping(
        createPaymentMethodConfigMapping(
          processors,
          ProcessorConfigCodes.PROCESSOR_FEE
        )
      );
      setPaymentMethodRoundingMapping(
        createPaymentMethodConfigMapping(
          processors,
          ProcessorConfigCodes.PROCESSOR_ROUND_TO_VALUE
        )
      );

      setProcessorConfigurations(
        processors.reduce<ProcessorConfigurations>((acc, processor) => {
          acc[processor.processorName] = processor.configurations;
          return acc;
        }, {} as ProcessorConfigurations)
      );

      onReady?.();
    } catch (error) {
      logger.error('Error fetching processors:', error);
    } finally {
      setLoading(false);
    }
  }, [service, isECommerceChannel]);

  // Trigger refetch when service is available
  useEffect(() => {
    fetchProcessors();
  }, [fetchProcessors]);

  // Memoize the filtered payment method device locations so it recalculates when `payment` or `paymentMethodByDeviceLocations` changes
  const filteredPaymentMethodByDeviceLocation = useMemo(() => {
    const paymentMethodDeviceLocations: PaymentMethodDeviceLocations = {};
    paymentMethodByDeviceLocations.forEach(paymentDeviceLocation => {
      const hasLocationFilter = !!payment?.locationId;
      const hasProcessorFilter = !!payment?.processorName;

      // If both locationId and processorName are provided, use AND logic
      const matchesBoth =
        hasLocationFilter && hasProcessorFilter
          ? (paymentDeviceLocation.posLocationId ||
              paymentDeviceLocation.locationId) === payment.locationId &&
            paymentDeviceLocation.processorName === payment.processorName
          : false;

      if (hasLocationFilter && hasProcessorFilter && !matchesBoth) {
        return;
      }
      // If only one of them is provided, use OR logic
      const matchesOne =
        (hasLocationFilter &&
          (paymentDeviceLocation.posLocationId ||
            paymentDeviceLocation.locationId) === payment.locationId) ||
        (hasProcessorFilter &&
          paymentDeviceLocation.processorName === payment.processorName);

      // If neither is provided, return all device locations
      const matches =
        hasLocationFilter || hasProcessorFilter
          ? matchesBoth || matchesOne
          : true;

      if (matches) {
        const filteredPaymentMethods =
          hasLocationFilter && hasProcessorFilter
            ? allProcessorsByPaymentMethods
            : defaultProcessorsByPaymentMethods;
        Object.keys(filteredPaymentMethods).forEach(method => {
          if (
            filteredPaymentMethods[method].includes(
              paymentDeviceLocation.processorName
            )
          ) {
            paymentMethodDeviceLocations[method] =
              paymentMethodDeviceLocations[method] || [];
            paymentMethodDeviceLocations[method].push(paymentDeviceLocation);
          }
        });
      }
    });

    return paymentMethodDeviceLocations;
  }, [
    payment,
    paymentMethodByDeviceLocations,
    allProcessorsByPaymentMethods,
    defaultProcessorsByPaymentMethods,
  ]);

  const filteredPaymentMethods = useMemo(() => {
    const filteredMethods: PaymentMethods = {};

    if (isECommerceChannel) {
      return groupProcessorsByPaymentMethod(processors);
    }

    const availableMethodNames = Object.keys(
      filteredPaymentMethodByDeviceLocation
    );

    const hasLocationFilter = !!payment?.locationId;
    const hasProcessorFilter = !!payment?.processorName;
    const paymentMethods_ =
      hasLocationFilter && hasProcessorFilter
        ? allProcessorsByPaymentMethods
        : defaultProcessorsByPaymentMethods;

    Object.keys(paymentMethods_)
      .filter((methodName: string) => methodName !== 'ACH')
      .forEach(methodName => {
        if (availableMethodNames.includes(methodName)) {
          if (!filteredMethods[methodName]) {
            filteredMethods[methodName] = paymentMethods_[methodName];
          }
        }
      });
    if (paymentMethods_.ACH) {
      filteredMethods.ACH = paymentMethods_.ACH;
    }

    return filteredMethods;
  }, [
    payment,
    paymentMethodByDeviceLocations,
    allProcessorsByPaymentMethods,
    defaultProcessorsByPaymentMethods,
    filteredPaymentMethodByDeviceLocation,
    isECommerceChannel,
  ]);

  return (
    <DeviceContext.Provider
      value={{
        paymentMethods: filteredPaymentMethods,
        paymentMethodByDeviceLocation: filteredPaymentMethodByDeviceLocation,
        paymentMethodFeeMapping: paymentMethodFeeMapping,
        processorFlowConfigByMethod,
        isPreTippingEnabled,
        isInvoicePaymentEnabled,
        getPaymentMethodFee,
        getPaymentMethodRoundingAmount,
        getPaymentMethodByProcessorId,
        getProcessorNameByProcessorId,
        getPaymentMethodByProcessorName,
        loading,
        refetch: fetchProcessors,
        getProcessorNameFeeMapping,
        getTippingEnabledPercentValues,
      }}
    >
      {children}
    </DeviceContext.Provider>
  );
};
