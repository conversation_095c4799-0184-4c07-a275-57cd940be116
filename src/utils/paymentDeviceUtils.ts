import { ProcessorConfigCodes } from '../context/DeviceProvider';
import { Processor } from '../models';
import { IProcessorFlowConfigByMethod } from '../types/processor';

export type PaymentMethods = { [key: string]: string[] };

export type PaymentMethodFeeMapping = { [key: string]: string };
export type PaymentMethodRoundingMapping = { [key: string]: string };

export const groupProcessorsByPaymentMethod = (
  processors: Processor[],
  shouldFilter?: boolean
): PaymentMethods => {
  return processors.reduce((acc: PaymentMethods, processor: Processor) => {
    if (shouldFilter) {
      const isDefault = processor.configurations.some(
        ({ category, code, value }) =>
          category?.includes('DEFAULT_PROCESSOR') &&
          code?.includes('DEFAULT') &&
          value === processor.processorName
      );

      if (processor.paymentMethod === 'ACH') {
        const isInvoicePaymentEnabled = processor.configurations.some(
          ({ code, value }) =>
            code === ProcessorConfigCodes.IS_INVOICE_PAYMENT_ENABLED &&
            value === 'true'
        );

        const isInStoreAchEnabled = processor.configurations.some(
          ({ code, value }) =>
            code === ProcessorConfigCodes.IN_STORE_ACH_ENABLED &&
            value === 'true'
        );
        if (!isInStoreAchEnabled && !isInvoicePaymentEnabled) {
          return acc;
        }
      } else {
        if (!isDefault) return acc;
      }
    }

    acc[processor.paymentMethod] = acc[processor.paymentMethod] || [];
    acc[processor.paymentMethod].push(processor.processorName);

    return acc;
  }, {});
};

export const createPaymentMethodConfigMapping = (
  processors: Processor[],
  codeName: string
): PaymentMethodFeeMapping => {
  return processors.reduce(
    (acc: PaymentMethodFeeMapping, processor: Processor) => {
      const processorConfig = processor.configurations?.find(
        ({ code }) => code === codeName
      );

      if (!processorConfig?.value) {
        return acc;
      }

      acc[processor.processorName] = processorConfig.value;

      return acc;
    },
    {}
  );
};

export const getECommerceEnabledProcessors = (processors: Processor[]) => {
  return processors.filter(processor => {
    if (processor.paymentMethod === 'ACH') {
      return processor.configurations.some(
        config =>
          config.code === ProcessorConfigCodes.IN_STORE_ACH_ENABLED &&
          config.value === 'true'
      );
    }
    if (processor.paymentMethod === 'Credit') {
      return processor.configurations.some(
        config =>
          config.code === ProcessorConfigCodes.IS_INVOICE_PAYMENT_ENABLED &&
          config.value === 'true'
      );
    }
    return false;
  });
};

export const groupProcessorFlowsByPaymentMethod = (
    processors: Processor[]
  ): IProcessorFlowConfigByMethod => {
    return processors.reduce<IProcessorFlowConfigByMethod>((acc, processor) => {
      if (!processor.paymentMethod || !processor.processorName) return acc;

      if (!processor.flows || processor.flows.length === 0) return acc;

      if (!acc[processor.paymentMethod]) {
        acc[processor.paymentMethod] = [];
      }

      acc[processor.paymentMethod].push({
        processorName: processor.processorName,
        flows: processor.flows,
      });

      return acc;
    }, {} as IProcessorFlowConfigByMethod);
};
