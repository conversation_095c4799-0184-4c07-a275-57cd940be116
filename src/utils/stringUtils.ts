import { toDollars } from './currencyUtils';

export const capitalize = (input: string | undefined): string => {
  if (!input) {
    return '';
  }
  return input.replace(/\b\w/g, char => char.toUpperCase());
};

export const formatFee = (fee: number | undefined | null): string => {
  if (!fee) {
    return '';
  }
  if (fee === -1) {
    return 'Variable fee';
  }
  if (fee > 0) {
    return `Fee: $${toDollars(fee)}`;
  }
  return '';
};

export const capitalizeFirst = (input: string | undefined): string => {
  if (!input || input.length === 0) {
    return '';
  }
  return input.charAt(0).toUpperCase() + input.slice(1);
};

export const formatUSPhoneNumber = (phoneNumber: string): string => {
  const cleaned = phoneNumber.replace(/\D/g, ''); // remove non-digits

  if (cleaned.length === 11 && cleaned.startsWith('1')) {
    phoneNumber = cleaned.slice(1); // remove leading '1'
  } else if (cleaned.length !== 10) {
    return '';
  } else {
    phoneNumber = cleaned;
  }

  // format into +1 (XXX)-XXX-XXXX
  const areaCode = phoneNumber.slice(0, 3);
  const firstPart = phoneNumber.slice(3, 6);
  const secondPart = phoneNumber.slice(6);

  return `+1 (${areaCode})-${firstPart}-${secondPart}`;
};
