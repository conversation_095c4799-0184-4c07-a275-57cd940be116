import {
  PaymentApprovedEvent,
  PaymentErrorEvent,
} from '../events/PaymentEvents';

export const createPaymentApprovedEvent = ({
  processorName,
  paymentMethod,
  data,
}: {
  processorName: string;
  paymentMethod: string;
  data: any;
}): PaymentApprovedEvent => ({
  eventType: 'PAYMENT_APPROVED',
  userMessage: 'Approved. Thank you!',
  processorName,
  paymentMethod,
  data,
});

export const createPaymentErrorEvent = ({
  userMessage,
  processorName,
  paymentMethod,
  data = {},
}: {
  userMessage: string;
  processorName: string;
  paymentMethod: string;
  data?: any;
}): PaymentErrorEvent => ({
  eventType: 'PAYMENT_ERROR',
  userMessage,
  processorName,
  paymentMethod,
  data,
});
