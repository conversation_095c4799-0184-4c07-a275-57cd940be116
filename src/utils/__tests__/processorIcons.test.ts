import { getProcessorIcon } from '../processorIcons';
import aeropay from '../../assets/aeropay.svg';
import stronghold from '../../assets/stronghold.svg';

describe('getProcessorIcon', () => {
  test('returns aeropay icon for "aeropay" processor', () => {
    expect(getProcessorIcon('aeropay')).toBe(aeropay);
  });

  test('returns aeropay icon for "aeropay_sandbox" processor', () => {
    expect(getProcessorIcon('aeropay_sandbox')).toBe(aeropay);
  });

  test('returns stronghold icon for "stronghold" processor', () => {
    expect(getProcessorIcon('stronghold')).toBe(stronghold);
  });

  test('returns stronghold icon for "stronghold_test" processor', () => {
    expect(getProcessorIcon('stronghold_test')).toBe(stronghold);
  });

  test('returns undefined for unknown processor', () => {
    expect(getProcessorIcon('unknown')).toBeUndefined();
  });
});
