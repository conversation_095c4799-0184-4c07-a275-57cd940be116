import { capitalize, formatUSPhoneNumber } from '../stringUtils';

describe('capitalize', () => {
  test('should capitalize the first letter of each word in a sentence', () => {
    const input = 'this is a test sentence.';
    const output = capitalize(input);
    expect(output).toBe('This Is A Test Sentence.');
  });

  test('should handle single word input', () => {
    const input = 'hello';
    const output = capitalize(input);
    expect(output).toBe('Hello');
  });

  test('should handle an empty string', () => {
    const input = '';
    const output = capitalize(input);
    expect(output).toBe('');
  });

  test('should handle strings with multiple spaces between words', () => {
    const input = 'this   is   a   test';
    const output = capitalize(input);
    expect(output).toBe('This   Is   A   Test');
  });

  test('should not change capitalization of other letters', () => {
    const input = 'tHis Is A tEst';
    const output = capitalize(input);
    expect(output).toBe('THis Is A TEst');
  });
});

describe('formatUSPhoneNumber', () => {
  test('formats valid US phone number without country code', () => {
    expect(formatUSPhoneNumber('1234567890')).toBe('+****************');
  });

  test('formats valid US phone number with country code', () => {
    expect(formatUSPhoneNumber('+11234567890')).toBe('+****************');
    expect(formatUSPhoneNumber('11234567890')).toBe('+****************');
  });

  test('removes non-numeric characters and formats correctly', () => {
    expect(formatUSPhoneNumber('(*************')).toBe('+****************');
    expect(formatUSPhoneNumber('************')).toBe('+****************');
    expect(formatUSPhoneNumber('  ************  ')).toBe('+****************');
  });

  test('returns empty string for invalid input', () => {
    expect(formatUSPhoneNumber('1234567')).toBe('');
    expect(formatUSPhoneNumber('12345678901232')).toBe('');
    expect(formatUSPhoneNumber('abc1234567')).toBe('');
    expect(formatUSPhoneNumber('abcdefg')).toBe('');
    expect(formatUSPhoneNumber('')).toBe('');
  });
});
