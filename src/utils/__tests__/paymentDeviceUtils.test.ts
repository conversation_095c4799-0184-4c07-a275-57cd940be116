import { Processor } from '../../models';
import { generateUUID } from '../../services/strings';
import {
  createPaymentMethodConfigMapping,
  groupProcessorFlowsByPaymentMethod,
  groupProcessorsByPaymentMethod,
} from '../paymentDeviceUtils';
import { PaymentMethod, ProcessorFlows } from '../../constants';

describe('groupProcessorsByPaymentMethod', () => {
  it('should group processors by payment method without filtering', () => {
    const processors: Processor[] = [
      {
        processorId: generateUUID(),
        processorName: 'ProcessorA',
        paymentMethod: 'CreditCard',
        configurations: [],
      },
      {
        processorId: generateUUID(),
        processorName: 'ProcessorB',
        paymentMethod: 'CreditCard',
        configurations: [],
      },
      {
        processorId: generateUUID(),
        processorName: 'ProcessorC',
        paymentMethod: 'ACH',
        configurations: [],
      },
    ];

    const result = groupProcessorsByPaymentMethod(processors);

    expect(result).toEqual({
      CreditCard: ['ProcessorA', 'ProcessorB'],
      ACH: ['ProcessorC'],
    });
  });

  it('should filter processors when shouldFilter is true', () => {
    const processors: Processor[] = [
      {
        processorId: generateUUID(),
        processorName: 'ProcessorA',
        paymentMethod: 'CreditCard',
        configurations: [
          {
            id: generateUUID(),
            name: '',
            isSecret: false,
            category: 'DEFAULT_PROCESSOR',
            code: 'DEFAULT',
            value: 'ProcessorA',
          },
        ],
      },
      {
        processorId: generateUUID(),
        processorName: 'ProcessorB',
        paymentMethod: 'CreditCard',
        configurations: [
          {
            id: generateUUID(),
            name: '',
            isSecret: false,
            category: 'DEFAULT_PROCESSOR',
            code: 'inStoreAchEnabled',
            value: 'false',
          },
        ],
      },
      {
        processorId: generateUUID(),
        processorName: 'ProcessorC',
        paymentMethod: 'ACH',
        configurations: [
          {
            id: generateUUID(),
            name: '',
            isSecret: false,
            category: '',
            code: 'inStoreAchEnabled',
            value: 'true',
          },
        ],
      },
      {
        processorId: generateUUID(),
        processorName: 'ProcessorD',
        paymentMethod: 'ACH',
        configurations: [
          {
            id: generateUUID(),
            name: '',
            isSecret: false,
            category: '',
            code: 'isCheckoutEnabled',
            value: 'false',
          },
        ],
      },
    ];

    const result = groupProcessorsByPaymentMethod(processors, true);

    expect(result).toEqual({
      CreditCard: ['ProcessorA'],
      ACH: ['ProcessorC'],
    });
  });

  it('should return an empty object when processors array is empty', () => {
    const processors: Processor[] = [];

    const result = groupProcessorsByPaymentMethod(processors);

    expect(result).toEqual({});
  });

  it('should handle processors with missing configurations gracefully', () => {
    const processors: Processor[] = [
      {
        processorId: generateUUID(),
        processorName: 'ProcessorA',
        paymentMethod: 'CreditCard',
        configurations: null as any, // Simulate missing configurations
      },
    ];

    const result = groupProcessorsByPaymentMethod(processors);

    expect(result).toEqual({
      CreditCard: ['ProcessorA'],
    });
  });
});

describe('createPaymentMethodFeeMapping', () => {
  it('should create a fee mapping for processors including ACH', () => {
    const processors: Processor[] = [
      {
        processorId: generateUUID(),
        processorName: 'ProcessorA',
        paymentMethod: 'CreditCard',
        configurations: [
          {
            id: generateUUID(),
            name: '',
            isSecret: false,
            category: '',
            code: 'processorFee',
            value: '2.5',
          },
        ],
      },
      {
        processorId: generateUUID(),
        processorName: 'ProcessorB',
        paymentMethod: 'DebitCard',
        configurations: [
          {
            id: generateUUID(),
            name: '',
            isSecret: false,
            category: '',
            code: 'processorFee',
            value: '1.5',
          },
        ],
      },
      {
        processorId: generateUUID(),
        processorName: 'ProcessorC',
        paymentMethod: 'ACH',
        configurations: [
          {
            id: generateUUID(),
            name: '',
            isSecret: false,
            category: '',
            code: 'processorFee',
            value: '0.5',
          },
        ],
      },
    ];

    const result = createPaymentMethodConfigMapping(processors, 'processorFee');

    expect(result).toEqual({
      ProcessorA: '2.5',
      ProcessorB: '1.5',
      ProcessorC: '0.5',
    });
  });

  it('should skip processors without processorFee configuration', () => {
    const processors: Processor[] = [
      {
        processorId: generateUUID(),
        processorName: 'ProcessorA',
        paymentMethod: 'CreditCard',
        configurations: [],
      },
      {
        processorId: generateUUID(),
        processorName: 'ProcessorB',
        paymentMethod: 'CreditCard',
        configurations: [
          {
            id: generateUUID(),
            name: '',
            isSecret: false,
            category: '',
            code: 'otherConfig',
            value: 'someValue',
          },
        ],
      },
      {
        processorId: generateUUID(),
        processorName: 'ProcessorC',
        paymentMethod: 'CreditCard',
        configurations: [
          {
            id: generateUUID(),
            name: '',
            isSecret: false,
            category: '',
            code: 'processorFee',
            value: '1.5',
          },
        ],
      },
    ];

    const result = createPaymentMethodConfigMapping(processors, 'processorFee');

    expect(result).toEqual({
      ProcessorC: '1.5',
    });
  });

  it('should handle processors with missing configurations', () => {
    const processors: Processor[] = [
      {
        processorId: generateUUID(),
        processorName: 'ProcessorA',
        paymentMethod: 'CreditCard',
        configurations: null as any,
      },
    ];

    const result = createPaymentMethodConfigMapping(processors, 'processorFee');

    expect(result).toEqual({});
  });

  it('should skip processors when processorFee configuration has no value', () => {
    const processors: Processor[] = [
      {
        processorId: generateUUID(),
        processorName: 'ProcessorA',
        paymentMethod: 'CreditCard',
        configurations: [
          {
            id: generateUUID(),
            name: '',
            isSecret: false,
            category: '',
            code: 'processorFee',
            value: '',
          },
        ],
      },
    ];

    const result = createPaymentMethodConfigMapping(processors, 'processorFee');

    expect(result).toEqual({});
  });
});

describe('groupProcessorFlowsByPaymentMethod', () => {
    it('groups processors by payment method with valid flows', () => {
      const processors: Processor[] = [
        {
          processorId: generateUUID(),
          processorName: 'ProcessorA',
          paymentMethod: PaymentMethod.Credit,
          configurations: [],
          flows: [ProcessorFlows.PAYMENT_LINK_SMS],
        },
        {
          processorId: generateUUID(),
          processorName: 'ProcessorB',
          paymentMethod: PaymentMethod.Credit,
          configurations: [],
          flows: [ProcessorFlows.PAYMENT_LINK_REDIRECT],
        },
        {
          processorId: generateUUID(),
          processorName: 'ProcessorC',
          paymentMethod: PaymentMethod.ATM,
          configurations: [],
          flows: [ProcessorFlows.TERMINAL_SALE],
        }
      ];

      const result = groupProcessorFlowsByPaymentMethod(processors);

      expect(result).toEqual({
        [PaymentMethod.Credit]: [
          {
            processorName: 'ProcessorA',
            flows: [ProcessorFlows.PAYMENT_LINK_SMS],
          },
          {
            processorName: 'ProcessorB',
            flows: [ProcessorFlows.PAYMENT_LINK_REDIRECT],
          },
        ],
        [PaymentMethod.ATM]: [
          {
            processorName: 'ProcessorC',
            flows: [ProcessorFlows.TERMINAL_SALE],
          },
        ],
      });
    });

    it('skips processors with empty flows', () => {
        const processors: Processor[] = [
          {
            processorId: generateUUID(),
            processorName: 'ProcessorD',
            paymentMethod: 'ACH',
            configurations: [],
            flows: [],
          },
        ];

        const result = groupProcessorFlowsByPaymentMethod(processors);
        expect(result).toEqual({});
      });

      it('skips processors with missing flows', () => {
        const processors: Processor[] = [
          {
            processorId: generateUUID(),
            processorName: 'ProcessorE',
            paymentMethod: 'ACH',
            configurations: [],
          },
        ];

        const result = groupProcessorFlowsByPaymentMethod(processors);
        expect(result).toEqual({});
      });

    it('returns empty object when input is an empty array', () => {
        const result = groupProcessorFlowsByPaymentMethod([]);
        expect(result).toEqual({});
      });
  });
