import { validateWidgetConfig } from '../validatorUtils';

describe('validateWidgetConfig', () => {
  test('should return "Valid configuration!" when all fields are valid', () => {
    const validConfig = {
      element: document.createElement('div'),
      debug: true,
      authTokenFactory: () => Promise.resolve('jwtstring'),
      getEntityId: () => Promise.resolve('entity-id-uuid'),
      theme: {
        positionFixed: true,
      },
      dispensaryShortName: 'test-ach1',
    };

    const result = validateWidgetConfig(validConfig);
    expect(result).toBe('Valid configuration!');
  });

  test('should throw an error if element is invalid', () => {
    const invalidConfig = {
      element: 'invalid-element',
      debug: true,
      authTokenFactory: () => Promise.resolve('jwtstring'),
      getEntityId: () => Promise.resolve('entity-id-uuid'),
    };

    expect(() => validateWidgetConfig(invalidConfig)).toThrow(
      'Invalid element: Must be a valid HTML element or undefined.'
    );
  });

  test('should throw an error if debug is not a boolean', () => {
    const invalidConfig = {
      debug: 'not-boolean',
      authTokenFactory: () => Promise.resolve('jwtstring'),
      getEntityId: () => Promise.resolve('entity-id-uuid'),
    };

    expect(() => validateWidgetConfig(invalidConfig)).toThrow(
      'Invalid debug: Must be a boolean value.'
    );
  });

  test('should throw an error if authTokenFactory is not a function', () => {
    const invalidConfig = {
      debug: true,
      authTokenFactory: 'not-a-function',
      getEntityId: () => Promise.resolve('entity-id-uuid'),
    };

    expect(() => validateWidgetConfig(invalidConfig)).toThrow(
      'Invalid authTokenFactory: Must be a function that returns a Promise.'
    );
  });

  test('should throw an error if getEntityId is not a function', () => {
    const invalidConfig = {
      debug: true,
      authTokenFactory: () => Promise.resolve('jwtstring'),
      getEntityId: 'not-a-function',
    };

    expect(() => validateWidgetConfig(invalidConfig)).toThrow(
      'Invalid getEntityId: Must be a function that returns a Promise.'
    );
  });

  test('should throw an error if theme.positionFixed is not a boolean', () => {
    const invalidConfig = {
      debug: true,
      authTokenFactory: () => Promise.resolve('jwtstring'),
      getEntityId: () => Promise.resolve('entity-id-uuid'),
      theme: {
        positionFixed: 'not-boolean',
      },
    };

    expect(() => validateWidgetConfig(invalidConfig)).toThrow(
      'Invalid theme.positionFixed: Must be a boolean.'
    );
  });

  test('should throw an error if dispensaryShortName is not in kebab-case', () => {
    const invalidConfig = {
      debug: true,
      authTokenFactory: () => Promise.resolve('jwtstring'),
      getEntityId: () => Promise.resolve('entity-id-uuid'),
      dispensaryShortName: 'invalidShortName',
    };

    expect(() => validateWidgetConfig(invalidConfig)).toThrow(
      'Invalid dispensaryShortName: Must be in kebab-case format.'
    );
  });

  test('should throw an error if dispensaryShortName exceeds 80 characters', () => {
    const longName = 'a'.repeat(81);
    const invalidConfig = {
      debug: true,
      authTokenFactory: () => Promise.resolve('jwtstring'),
      getEntityId: () => Promise.resolve('entity-id-uuid'),
      dispensaryShortName: longName,
    };

    expect(() => validateWidgetConfig(invalidConfig)).toThrow(
      'Invalid dispensaryShortName: Must be at most 80 characters long.'
    );
  });
});
