import { generateReferenceNumber } from '../requestUtils';
import 'reflect-metadata';

describe('generateReferenceNumber', () => {
  it('should generate reference number with dispensaryShortName', () => {
    const result = generateReferenceNumber('ABC123', 1, 'DISP');
    expect(result).toBe('DISP-TICKET-ABC123-1');
  });

  it('should generate reference number without dispensaryShortName', () => {
    const result = generateReferenceNumber('XYZ789', 2);
    expect(result).toBe('TICKET-XYZ789-2');
  });

  it('should handle empty dispensaryShortName', () => {
    const result = generateReferenceNumber('LMN456', 3, '');
    expect(result).toBe('TICKET-LMN456-3');
  });

  it('should handle undefined dispensaryShortName', () => {
    const result = generateReferenceNumber('DEF345', 4, undefined);
    expect(result).toBe('TICKET-DEF345-4');
  });

  it('should handle paymentAttemptNumber as 0', () => {
    const result = generateReferenceNumber('JKL012', 0);
    expect(result).toBe('TICKET-JKL012-0');
  });
});
