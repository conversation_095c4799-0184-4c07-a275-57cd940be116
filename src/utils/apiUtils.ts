import { AxiosError } from 'axios';

export interface ParsedErrorDetails {
  method?: string;
  endpoint?: string;
  message: string;
  statusCode?: number | null;
}

export const parseAxiosError = (error: AxiosError): ParsedErrorDetails => {
  const errorDetails: ParsedErrorDetails = {
    method: '',
    endpoint: '',
    message: 'An error occurred',
    statusCode: null,
  };

  if (error.response) {
    errorDetails.statusCode = error.response.status;
    errorDetails.message = error.response.data?.message || error.message;

    errorDetails.method = error?.config?.method?.toUpperCase() || '';
    errorDetails.endpoint =
      (error?.config?.baseURL || '') + (error?.config?.url || '');
  } else if (error.request) {
    errorDetails.message = 'Network error or no response received';
  } else {
    errorDetails.message = error.message;
  }

  return errorDetails;
};
