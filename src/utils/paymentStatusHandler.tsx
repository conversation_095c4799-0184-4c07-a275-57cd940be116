import { h } from 'preact';
import { PaymentCanceledEvent } from '../events/PaymentEvents';
import {
  PaymentApprovedLayout,
  PaymentErrorLayout,
  VTPaymentAuthorizedLayout,
  ACHPaymentPreparingLayout,
} from '../layout/OverlayCustomComponents';
import { capitalize } from './stringUtils';
import {
  createPaymentApprovedEvent,
  createPaymentErrorEvent,
} from './paymentEventCreators';
import { Channel, PaymentMethod } from '../models';
import CaptureCharge from '../components/channels/virtualTerminal/captureCharge';
interface StatusHandlerParams {
  status: string;
  data: any;
  processorName: string;
  paymentMethod: string;
  payment: any;
  setOverlayVisible: (visible: boolean) => void;
  setOverlayContext: (context: any) => void;
  primaryActionButton: any;
  changePaymentMethodButton: any;
  channel: Channel;
  newTabRef?: any;
}

export function handlePaymentStatus({
  status,
  data,
  processorName,
  paymentMethod,
  payment,
  setOverlayVisible,
  setOverlayContext,
  primaryActionButton,
  changePaymentMethodButton,
  channel,
  newTabRef,
}: StatusHandlerParams) {
  if (status === 'canceled') {
    setOverlayVisible(true);
    const canceledEvent: PaymentCanceledEvent = {
      eventType: 'PAYMENT_CANCELED',
      userMessage: 'Transaction Canceled',
      processorName,
      paymentMethod,
      data,
    };

    payment.handlePaymentResponse(canceledEvent);

    setOverlayContext({
      customComponent: (
        <PaymentErrorLayout
          errorMessage={capitalize(
            data.processorResponseDetailMessage || canceledEvent.userMessage
          )}
          subErrorMessage={data.displayErrorMessage || 'Something went wrong.'}
          primaryActionButton={primaryActionButton}
          secondaryActionButton={changePaymentMethodButton}
        />
      ),
      disableButtonsOnProgress: true,
    });
  }

  if (
    ['error', 'terminal error', 'terminal busy', 'declined'].includes(status)
  ) {
    setOverlayVisible(true);
    const errorEvent = createPaymentErrorEvent({
      userMessage: data.processorResponseDetailMessage,
      processorName,
      paymentMethod,
      data: status === 'declined' ? {} : data,
    });

    payment.handlePaymentResponse(errorEvent);

    setOverlayContext({
      customComponent: (
        <PaymentErrorLayout
          errorMessage={capitalize(
            data.processorResponseDetailMessage ||
              'An unexpected error occurred'
          )}
          subErrorMessage={data.displayErrorMessage}
          primaryActionButton={primaryActionButton}
          secondaryActionButton={changePaymentMethodButton}
          channel={channel}
        />
      ),
      disableButtonsOnProgress: true,
    });
  }

  if (status === 'approved' || status === 'submitted') {
    if (newTabRef?.current?.tab && !newTabRef.current.tab.closed) {
      newTabRef.current.tab.close();
      newTabRef.current.tab = null;
      newTabRef.current.url = null;
    }

    const approvedEvent = createPaymentApprovedEvent({
      processorName,
      paymentMethod,
      data,
    });

    payment.handlePaymentResponse(approvedEvent);

    if (channel === Channel.ECOMMERCE) {
      setOverlayVisible(false);
      return;
    }

    setOverlayVisible(true);

    if (channel === Channel.VIRTUAL_TERMINAL && data.method?.toLowerCase() === PaymentMethod.ACH) {
      setOverlayContext({
        customComponent: <ACHPaymentPreparingLayout />,
        disableButtonsOnProgress: true,
      });

      setTimeout(() => {
        setOverlayContext({
          customComponent: <VTPaymentAuthorizedLayout />,
          disableButtonsOnProgress: true,
        });
      }, 1500);

      setTimeout(() => {
        setOverlayContext({
          customComponent: (
            <CaptureCharge
              paymentMethod={paymentMethod}
              processorName={processorName}
              paymentChargeId={data.id}
            />
          ),
          disableButtonsOnProgress: true,
        });
      }, 5000);
    } else {
      setOverlayContext({
        customComponent: (
          <PaymentApprovedLayout
            paymentMethod={data.method}
            providerName={processorName}
            totalAmount={data.originalAmount}
            tipAmount={data.tipAmount}
            feeAmount={data.feeAmount}
            entityName={payment.entityName}
            channel={channel}
            cashbackAmount={data.cashbackAmount}
            createdAt={data.updatedAt}
          />
        ),
        disableButtonsOnProgress: true,
      });
    }
  }

  return null;
}
