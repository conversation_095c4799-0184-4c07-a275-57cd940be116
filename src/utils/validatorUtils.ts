import { ValidationError } from 'class-validator';

export const printValidationErrors = (errors: ValidationError[]) => {
  if (errors.length === 0) {
    return;
  }

  console.log('Validation failed. Errors:');

  errors.forEach(error => {
    console.log(`Property: ${error.property}`);

    if (error.constraints) {
      // Constraints contain the validation error messages
      Object.values(error.constraints).forEach(message => {
        console.log(` - ${message}`);
      });
    }

    // Handle nested validation errors (e.g., objects within objects)
    if (error.children && error.children.length > 0) {
      error.children.forEach(childError => {
        printNestedValidationErrors(childError);
      });
    }
  });
};

// Helper function to print nested errors recursively
function printNestedValidationErrors(error: ValidationError): void {
  console.log(`Nested Property: ${error.property}`);

  if (error.constraints) {
    Object.values(error.constraints).forEach(message => {
      console.log(` - ${message}`);
    });
  }

  if (error.children && error.children.length > 0) {
    error.children.forEach(childError => {
      printNestedValidationErrors(childError);
    });
  }
}

export const validateWidgetConfig = (config: any) => {
  const errors = [];

  // Validate element
  if (
    config.element !== undefined &&
    !(config.element instanceof HTMLElement)
  ) {
    errors.push('Invalid element: Must be a valid HTML element or undefined.');
  }

  // Validate debug
  if (typeof config.debug !== 'boolean') {
    errors.push('Invalid debug: Must be a boolean value.');
  }

  // Validate authTokenFactory
  if (typeof config.authTokenFactory !== 'function') {
    errors.push(
      'Invalid authTokenFactory: Must be a function that returns a Promise.'
    );
  }

  // Validate getEntityId
  if (typeof config.getEntityId !== 'function') {
    errors.push(
      'Invalid getEntityId: Must be a function that returns a Promise.'
    );
  }

  // Validate theme (if present)
  if (config.theme !== undefined) {
    if (typeof config.theme !== 'object') {
      errors.push('Invalid theme: Must be an object.');
    } else {
      if (
        'positionFixed' in config.theme &&
        typeof config.theme.positionFixed !== 'boolean'
      ) {
        errors.push('Invalid theme.positionFixed: Must be a boolean.');
      }
    }
  }

  // Validate dispensaryShortName (optional)
  if (config.dispensaryShortName !== undefined) {
    if (typeof config.dispensaryShortName !== 'string') {
      errors.push('Invalid dispensaryShortName: Must be a string.');
    } else if (config.dispensaryShortName.length > 80) {
      errors.push(
        'Invalid dispensaryShortName: Must be at most 80 characters long.'
      );
    }
  }

  if (errors.length > 0) {
    throw new Error(
      `Configuration validation failed: \n- ${errors.join('\n- ')}`
    );
  }

  return 'Valid configuration!';
};
