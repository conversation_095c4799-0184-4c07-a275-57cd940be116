import 'core-js/stable';
import 'regenerator-runtime/runtime';
import './sentryInstrumentation';
import './layout/styles/font.css';
import { h, render } from 'preact';
import { App } from './App';
import loader from './loader';
import { Channel, Configurations } from './models';
import { generateUUID } from './services/strings';

/**
 * Default configurations that are overridden by
 * parameters in embedded script.
 */
const defaultConfig: Configurations = {
  getEntityId: () => Promise.resolve(''),
  sessionId: generateUUID(),
  debug: false,
  isWebSocketEnabled: true,
  serviceBaseUrl:
    process.env.TP_GATEWAY_API_ENDPOINT ||
    'https://payment-gateway-api.dev.treezpay.com/v0',
  autoRenderPayments: true,
  disableDarkMode: true,
  text: {},
  styles: {},
  payment: undefined,
  theme: { positionFixed: true },
  authTokenFactory: () => Promise.resolve(''),
  dispensaryShortName: 'PaySDK',
  channel: Channel.FFD,
};

// main entry point - calls loader and render Preact app into supplied element
loader(window, defaultConfig, window.document.currentScript, (el, config) =>
  render(h(App, { ...config, element: el }), el)
);
