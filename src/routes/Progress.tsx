import { h } from 'preact';
import { useEffect, useState } from 'preact/hooks';

const STYLE_OUTER = 'width:100%;z-index:9999;top:0';
const STYLE_INNER = `
  height: 2px;
  background-color: var(--progress-color);
  animation: progress-animation 1.5s infinite linear;
  position: absolute;
`;

const keyframes = `
  @keyframes progress-animation {
    0% {
      left: -50%;
      width: 50%;
    }
    50% {
      left: 50%;
      width: 100%;
    }
    100% {
      left: 100%;
      width: 50%;
    }
  }
`;

type ProgressProps = {
    id?: string;
    className?: string;
    height?: string;
    color?: string;
    indeterminate?: boolean;
    value?: number;
    onStart?: (component: any, value: number) => void;
    onChange?: (component: any, value: number) => void;
    onComplete?: (component: any) => void;
};

let timer: any;
const Progress = ({ id, className, height = '4px', color = 'black', indeterminate = false, value = 0, onStart, onChange, onComplete }: ProgressProps) => {
    const [val, setVal] = useState(value);

    useEffect(() => {
        if (!indeterminate) {
            setVal(limit(value));
        }
    }, [value, indeterminate]);

    useEffect(() => {
        if (!indeterminate) {
            clearInterval(timer);
            if (onStart) onStart(null, val);

            timer = setInterval(() => {
                setVal((val) => Number((val * 1.3).toFixed(2)));
            }, 800);

            if (val >= 100) {
                if (onComplete) onComplete(null);
                clearInterval(timer);
            } else {
                if (onChange) onChange(null, val);
            }

            return () => clearInterval(timer);
        }
    }, [val, indeterminate]);

    return (
        <div id={id} className={className} style={`${STYLE_OUTER};height:${height};position:absolute;`}>
            <style>{keyframes}</style>
            <div style={`
                ${STYLE_INNER.replace('var(--progress-color)', color)};
                ${indeterminate ? '' : `width:${val}%;transition:all 200ms ease;`}
            `}></div>
        </div>
    );
};

export default Progress;

const limit = (v: number) => Math.min(v || 0, 100);
