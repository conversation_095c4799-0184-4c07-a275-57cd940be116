import { FunctionalComponent, h } from 'preact';
import { useContext } from 'preact/hooks';
import { GlobalsContext } from '../AppContext';
import { PaymentDeviceLocation } from '../models';
import PaymentOptionsContainer from '../components/payments/PaymentOptionsContainer';
import CreditCardIcon from '../assets/credit.svg';
import DebitCardIcon from '../assets/debit.svg';
import { DeviceContext } from '../context/DeviceProvider';
import useLogger from '../hooks/useLogger';

const PAYMENT_METHOD_ICON = {
  Debit: DebitCardIcon,
  Credit: CreditCardIcon,
};

const PaymentOptions: FunctionalComponent<{ paymentMethod: string }> = ({
  paymentMethod,
}) => {
  const logger = useLogger();

  const {
    payment,
    navigate,
    setOverlayVisible,
    setPaymentProcessor,
    setOverlayMessage,
  } = useContext(GlobalsContext);
  const deviceContext = useContext(DeviceContext);

  if (!deviceContext) {
    return <div>Loading...</div>;
  }

  if (!payment) {
    setOverlayVisible(true);
    setOverlayMessage(`Waiting for Payment Information`);
    setTimeout(() => {
      navigate('/');
      setOverlayVisible(false);
    }, 2000);
    return <div>Waiting for Payment Information</div>;
  }

  const {
    paymentMethodByDeviceLocation,
    paymentMethodFeeMapping,
    isPreTippingEnabled,
  } = deviceContext;

  const getPaymentOptions = () => {
    const icon =
      PAYMENT_METHOD_ICON[paymentMethod as keyof typeof PAYMENT_METHOD_ICON];

    return paymentMethodByDeviceLocation[paymentMethod]?.map(
      (paymentDevice: PaymentDeviceLocation) => {
        return {
          processorName: paymentDevice.processorName,
          label: paymentDevice.name,
          fee: paymentMethodFeeMapping[paymentDevice.processorName]
            ? `${paymentMethodFeeMapping[paymentDevice.processorName]}`
            : '',
          icon,
          onClick: () => {
            setPaymentProcessor({
              paymentMethod,
              locationId:
                paymentDevice.posLocationId || paymentDevice.locationId,
              processorId: paymentDevice.processorId,
              processorName: paymentDevice.processorName,
              paymentDeviceConfigurationId: paymentDevice.id,
              paymentSent: false,
            });

            if (isPreTippingEnabled(paymentDevice.processorName)) {
              navigate('/tips');
            } else {
              navigate('/summary');
            }
          },
        };
      }
    );
  };

  const paymentOptions = getPaymentOptions() || [];

  if (paymentOptions.length === 1) {
    logger.debug(
      `Only one payment device for the selected method: ${paymentOptions[0].label}`
    );
    logger.debug(`Processor selected: ${paymentOptions[0].processorName}`);
    paymentOptions[0].onClick();
    return null; // Return null as there's no UI to render
  }

  return (
    <PaymentOptionsContainer
      paymentOptions={paymentOptions}
      id={`tz-${paymentMethod?.toLowerCase()}-options`}
    />
  );
};

export default PaymentOptions;
