import { h, Fragment, FunctionalComponent } from 'preact';
import { ConfigContext, GlobalsContext } from '../AppContext';
import { useContext, useEffect } from 'preact/hooks';
import PaymentSummary from '../components/payments/PaymentSummary';
import { PreviewContext } from '../context/PreviewProvider';
import useNavigateBack from '../hooks/useNavigateBack';
import useLogger from '../hooks/useLogger';
import { Channel } from '../models';

const Checkout: FunctionalComponent = () => {
  const logger = useLogger();
  const config = useContext(ConfigContext);

  const {
    history,
    payment,
    setPayment,
    navigate,
    paymentProcessor,
    setPaymentProcessor,
  } = useContext(GlobalsContext);
  const previewContext = useContext(PreviewContext);
  const navigateBack = useNavigateBack();

  const isCheckoutDisabled = previewContext?.loading;

  useEffect(() => {
    if (previewContext && paymentProcessor?.processorName && payment) {
      previewContext.fetchPreviewPaymentTotals(paymentProcessor.processorName);
    }
  }, [payment, paymentProcessor]);

  const handleSubmit = () => {
    if (!payment) {
      logger.error('No payment object available');
      return;
    }

    if (paymentProcessor) {
      setPaymentProcessor({
        ...paymentProcessor,
        paymentSent: false,
      });
    }

    navigate('/paymentProcessing/');
  };

  const handleBack = () => {
    if (previewContext) {
      previewContext.reset();
    }
    if (!history.includes('/tips')) {
      setPaymentProcessor(undefined);
    }

    if (history.includes('/paymentProcessing/prepaymentOption') && payment) {
      setPayment({
        ...payment,
        processorName: undefined,
      });
      navigate('/');
    } else {
      navigateBack();
    }
  };

  if (config.channel === Channel.VIRTUAL_TERMINAL) {
    handleSubmit();
    return null;
  }

  return (
    <Fragment>
      {!payment ? (
        <p>No payment information available.</p>
      ) : (
        <PaymentSummary
          payment={payment}
          handleSubmit={handleSubmit}
          handleBack={handleBack}
          isCheckoutDisabled={isCheckoutDisabled}
        />
      )}
    </Fragment>
  );
};

export default Checkout;
