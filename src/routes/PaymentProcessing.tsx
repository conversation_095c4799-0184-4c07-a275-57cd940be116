import { h, FunctionalComponent } from 'preact';
import { useContext, useEffect, useCallback, useMemo } from 'preact/hooks';
import { ConfigContext, GlobalsContext, ServiceContext } from '../AppContext';
import { MSOTreezPayCreatePaymentResponse, Payment } from '../models';
import {
  PaymentCreatedEvent,
  PaymentErrorEvent,
  PaymentRequestPreparingEvent,
} from '../events/PaymentEvents';
import { DeviceContext } from '../context/DeviceProvider';
import {
  PaymentCancelationProgressLayout,
  PaymentErrorLayout,
  PaymentInProgressLayout,
  PaymentPreparingLayout,
} from '../layout/OverlayCustomComponents';
import { useRoute } from 'wouter-preact';
import { PreviewContext } from '../context/PreviewProvider';
import { generateReferenceNumber } from '../utils/requestUtils';
import useLogger from '../hooks/useLogger';
import { capitalize, capitalizeFirst } from '../utils/stringUtils';
import { useGeneratePaymentLink } from '../hooks/useGeneratePaymentLink';
import { usePaymentActionButtons } from '../hooks/usePaymentActionButtons';
import { usePaymentStatusPolling } from '../components/handlers/PaymentStatusPollingHandler';

const PaymentProcessing: FunctionalComponent = () => {
  const logger = useLogger();

  const config = useContext(ConfigContext);
  const service = useContext(ServiceContext);
  const deviceContext = useContext(DeviceContext);
  const previewContext = useContext(PreviewContext);

  const { startPolling } = usePaymentStatusPolling();

  const {
    setOverlayContext,
    setOverlayVisible,
    setOverlayMessage,
    navigate,
    setPayment,
    payment,
    paymentProcessor,
    setPaymentProcessor,
  } = useContext(GlobalsContext);
  const { generatePaymentLink } = useGeneratePaymentLink();
  const [_match, params] = useRoute('/paymentProcessing/*');
  const isPrepaymentOption = params
    ? params['0'] === 'prepaymentOption'
    : false;

  const totalAmount = useMemo(() => {
    if (!payment) return 0;

    const { originalAmount } = payment;
    return previewContext?.previewTotals?.totalAmount
      ? Number(previewContext.previewTotals.totalAmount)
      : originalAmount;
  }, [payment, previewContext]);

  if (!deviceContext) {
    setOverlayVisible(true);
    setOverlayMessage(`It looks like we're missing device context!`);
    setTimeout(() => {
      navigate('/');
      setOverlayVisible(false);
    }, 2000);
    return <div>Loading...</div>;
  }

  if (!paymentProcessor) {
    setOverlayVisible(true);
    setOverlayMessage(`It looks like we're missing processor context!`);
    setTimeout(() => {
      navigate(-1);
      setOverlayVisible(false);
    }, 2000);
    return <div>Payment Processor And Device is not selected...</div>;
  }

  if (!payment) {
    setOverlayVisible(true);
    setOverlayMessage(`Waiting for Payment Information`);
    setTimeout(() => {
      navigate('/');
      setOverlayVisible(false);
    }, 2000);
    return <div>Waiting for Payment Information</div>;
  }

  const { primaryActionButton, changePaymentMethodButton } =
    usePaymentActionButtons({});

  const cancelPayment = useCallback(
    async (paymentId: string) => {
      const { handlePaymentResponse } = payment;
      const { processorName, paymentMethod } = paymentProcessor;

      if (!paymentId) {
        navigate(-1);
        return;
      }

      setOverlayContext({
        customComponent: (
          <PaymentCancelationProgressLayout totalAmount={totalAmount} />
        ),
        disableButtonsOnProgress: true,
      });

      try {
        const res = await service?.cancelPayment(paymentId);
        if (res?.data.canceled) {
          logger.debug('Cancel payment successfully sent.');
        } else {
          logger.debug(
            'Error canceling payment. Awaiting the gw async response. Sync Request response: ',
            res
          );
          const errorMessage = res?.message || 'An unexpected error occurred';
          const errorEvent: PaymentErrorEvent = {
            eventType: 'PAYMENT_ERROR',
            userMessage: 'Error canceling payment. See logs.',
            processorName,
            paymentMethod,
            data: {},
          };

          handlePaymentResponse(errorEvent);

          setOverlayContext({
            customComponent: (
              <PaymentErrorLayout
                errorMessage={errorMessage}
                primaryActionButton={primaryActionButton}
                secondaryActionButton={changePaymentMethodButton}
              />
            ),
          });
        }
        return null;
      } catch (error) {
        logger.error('Cancel payment error:', error);

        const errorMessage =
          error?.data?.message || 'An unexpected error occurred';
        const errorEvent: PaymentErrorEvent = {
          eventType: 'PAYMENT_ERROR',
          userMessage: 'Error canceling payment. See logs.',
          processorName,
          paymentMethod,
          data: {},
        };

        handlePaymentResponse(errorEvent);

        setOverlayContext({
          customComponent: (
            <PaymentErrorLayout
              errorMessage={errorMessage}
              subErrorMessage={
                error?.data?.displayErrorMessage || errorEvent?.userMessage
              }
              primaryActionButton={primaryActionButton}
              secondaryActionButton={changePaymentMethodButton}
            />
          ),
        });
      }

      return null;
    },
    [
      payment,
      paymentProcessor,
      navigate,
      setOverlayContext,
      service,
      totalAmount,
    ]
  );

  useEffect(() => {
    let isMounted = true;

    const handleCancelPayment = async () => {
      if (isMounted && payment?.cancel && payment.paymentId) {
        setPayment((payment: Payment) => ({
          ...payment,
          cancel: false,
          paymentId: undefined,
          invoiceId: undefined,
        }));
        await cancelPayment(payment.paymentId);
      }
    };

    handleCancelPayment();

    return () => {
      isMounted = false;
    };
  }, [payment, cancelPayment, setPayment]);

  const sendPayment = useCallback(
    async ({
      processorId,
      processorName,
      paymentDeviceConfigurationId,
    }: {
      processorId: string;
      processorName: string;
      paymentDeviceConfigurationId: string;
    }) => {
      const { paymentMethod } = paymentProcessor;

      setOverlayVisible(true);
      setOverlayContext({
        customComponent: <PaymentPreparingLayout channel={config.channel} />,
        disableButtonsOnProgress: true,
      });
      const {
        originalAmount,
        employeeReferenceId,
        transactingAssociateId,
        customer,
        ticketId,
        ticketAlphaNumId,
        handlePaymentResponse,
        integratedPaymentAttempts,
        tip,
      } = payment;
      const paymentAttemptNumber = (integratedPaymentAttempts || 0) + 1;

      const requestPreparingEvent: PaymentRequestPreparingEvent = {
        eventType: 'PAYMENT_REQUEST_PREPARING',
        userMessage: 'Connecting to payment terminals...',
        processorName,
        paymentMethod,
        data: {
          originalAmount: totalAmount,
          employeeReferenceId,
          customer,
          ticketId,
          ticketAlphaNumId,
        },
      };

      handlePaymentResponse(requestPreparingEvent);

      try {
        const isInvoicePaymentEnabled =
          deviceContext.isInvoicePaymentEnabled(processorName);
        let res;
        if (isInvoicePaymentEnabled) {
          res = await generatePaymentLink(processorName, paymentMethod);
        } else {
          res = await service?.createPayment({
            sessionId: config.sessionId,
            originalAmount,
            referenceNumber: generateReferenceNumber(
              ticketAlphaNumId,
              paymentAttemptNumber,
              config.dispensaryShortName
            ),
            employeeReferenceId,
            transactingAssociateId,
            customer,
            ticketId,
            ticketAlphaNumId,
            feeAmount: 0,
            tipAmount: tip,
            processorId,
            processorName,
            paymentDeviceConfigurationId,
            triggerProcessor: true,
          });
        }

        const paymentId = res?.data.id;
        if (!paymentId) {
          throw new Error('No payment ID received from create payment');
        }

        setPayment({
          ...payment,
          [isInvoicePaymentEnabled ? 'invoiceId' : 'paymentId']: paymentId,
          asyncUpdateReceived: false,
          integratedPaymentAttempts: paymentAttemptNumber,
        });

        if (isInvoicePaymentEnabled) {
          startPolling(paymentId, 'invoice');
        } else {
          startPolling(paymentId, 'payment');
        }

        const createdEvent: PaymentCreatedEvent = {
          eventType: 'PAYMENT_CREATED',
          userMessage: 'Please proceed on the terminal',
          processorName,
          paymentMethod,
          data: res?.data as MSOTreezPayCreatePaymentResponse,
        };

        handlePaymentResponse(createdEvent);

        setOverlayContext({
          customComponent: (
            <PaymentInProgressLayout
              totalAmount={totalAmount}
              channel={config.channel}
            />
          ),
        });
      } catch (error) {
        logger.error('createPayment error:', error);
        const errorMessage =
          error?.data?.processorResponseDetailMessage ||
          'An unexpected error occurred';
        const message = error?.data?.message;
        const errorRaw = error?.data?.errorRaw;
        const userMessage =
          message || errorRaw
            ? [message, errorRaw ? capitalizeFirst(errorRaw) : null]
                .filter(Boolean)
                .join(' ')
            : 'Error creating payment.';

        const errorEvent: PaymentErrorEvent = {
          eventType: 'PAYMENT_ERROR',
          userMessage,
          processorName,
          paymentMethod,
          data: {},
        };

        handlePaymentResponse(errorEvent);

        setOverlayContext({
          customComponent: (
            <PaymentErrorLayout
              errorMessage={capitalize(errorMessage)}
              subErrorMessage={
                error?.data?.displayErrorMessage || errorEvent.userMessage
              }
              primaryActionButton={primaryActionButton}
              secondaryActionButton={changePaymentMethodButton}
            />
          ),
        });
      }
    },
    [
      paymentProcessor,
      setOverlayVisible,
      setOverlayContext,
      config.sessionId,
      payment,
      totalAmount,
      service,
      setPayment,
      cancelPayment,
    ]
  );

  useEffect(() => {
    const {
      paymentSent,
      processorId,
      processorName,
      paymentDeviceConfigurationId,
    } = paymentProcessor;
    const { isPreTippingEnabled } = deviceContext;
    // Send payment if valid processor details exist
    if (
      !paymentSent &&
      processorId &&
      processorName &&
      paymentDeviceConfigurationId
    ) {
      if (isPrepaymentOption) {
        if (isPreTippingEnabled(processorName)) {
          navigate('/tips');
        } else {
          navigate('/summary');
        }
      } else {
        setPaymentProcessor({ ...paymentProcessor, paymentSent: true });
        sendPayment({
          processorId,
          processorName,
          paymentDeviceConfigurationId,
        });
      }
    }
  }, [
    paymentProcessor,
    isPrepaymentOption,
    deviceContext,
    navigate,
    setPaymentProcessor,
    sendPayment,
    payment,
  ]);

  return null;
};

export default PaymentProcessing;
