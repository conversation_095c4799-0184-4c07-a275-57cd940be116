import { h, FunctionalComponent } from 'preact';
import { useCallback, useContext, useEffect, useMemo } from 'preact/hooks';
import TipComponent from '../components/tip';
import { GlobalsContext } from '../AppContext';
import { PreviewContext } from '../context/PreviewProvider';
import { dollarsToCents } from '../utils/currencyUtils';
import useNavigateBack from '../hooks/useNavigateBack';
import useLogger from '../hooks/useLogger';
import useResetPayment from '../hooks/useResetPayment';
import { DeviceContext } from '../context/DeviceProvider';

const TipPayments: FunctionalComponent = () => {
  const logger = useLogger();

  const {
    history,
    navigate,
    paymentProcessor,
    setPaymentProcessor,
    payment,
    setPayment,
  } = useContext(GlobalsContext);
  const previewContext = useContext(PreviewContext);
  const deviceContext = useContext(DeviceContext);

  const navigateBack = useNavigateBack();
  const resetPayment = useResetPayment();

  useEffect(() => {
    if (!previewContext) return;

    const { fetchPreviewPaymentTotals, loading, previewTotals } =
      previewContext;

    if (
      paymentProcessor?.processorName &&
      !loading &&
      !previewTotals?.totalAmount
    ) {
      fetchPreviewPaymentTotals(paymentProcessor.processorName, 0);
    }
  }, [paymentProcessor?.processorName, previewContext]);

  const handleTipSubmit = useCallback(
    (tip: number) => {
      if (!payment) {
        logger.error('No payment object available');
        return;
      }

      setPayment({
        ...payment,
        tip: Number(dollarsToCents(tip)),
      });

      if (paymentProcessor) {
        setPaymentProcessor({
          ...paymentProcessor,
          paymentSent: false,
        });
      }

      navigate('/paymentProcessing/');
    },
    [payment, paymentProcessor, setPayment, setPaymentProcessor, navigate]
  );

  const handleBack = useCallback(() => {
    if (!payment) {
      logger.error('No payment object available');
      return;
    }

    resetPayment();

    if (history.includes('/paymentProcessing/prepaymentOption')) {
      navigate('/');
    } else {
      navigateBack();
    }
  }, [setPaymentProcessor, navigate, payment]);

  const tipsVariants = useMemo(() => {
    if (
      paymentProcessor?.processorName &&
      deviceContext?.getTippingEnabledPercentValues
    ) {
      const tipValues = deviceContext.getTippingEnabledPercentValues(
        paymentProcessor.processorName
      );
      return [0, ...tipValues]; // include no tip
    }

    return [];
  }, [deviceContext?.getTippingEnabledPercentValues]);

  return (
    <TipComponent
      tipsVariants={tipsVariants}
      onSubmit={handleTipSubmit}
      handleBack={handleBack}
    />
  );
};

export default TipPayments;
