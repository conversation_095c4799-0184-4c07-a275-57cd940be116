import { h, FunctionalComponent } from 'preact';
import { useContext } from 'preact/hooks';
import { GlobalsContext } from '../AppContext';
import PaymentOptionsContainer from '../components/payments/PaymentOptionsContainer';
import DebitCardIcon from '../components/icons/roundedDebit';
import { DeviceContext } from '../context/DeviceProvider';
import useLogger from '../hooks/useLogger';

const AtmPayments: FunctionalComponent = () => {
  const logger = useLogger();

  const deviceContext = useContext(DeviceContext);
  const {
    payment,
    navigate,
    setPaymentProcessor,
    setOverlayVisible,
    setOverlayMessage,
  } = useContext(GlobalsContext);

  if (!deviceContext) {
    return <div>Loading...</div>;
  }

  if (!payment) {
    setOverlayVisible(true);
    setOverlayMessage(`Waiting for Payment Information`);
    setTimeout(() => {
      navigate('/');
      setOverlayVisible(false);
    }, 2000);
    return <div>Waiting for Payment Information</div>;
  }

  const {
    paymentMethodByDeviceLocation,
    paymentMethodFeeMapping,
    isPreTippingEnabled,
  } = deviceContext;

  const getPaymentOptions = () => {
    return paymentMethodByDeviceLocation['ATM'].map(paymentDevice => {
      return {
        processorName: paymentDevice.processorName,
        label: paymentDevice.name, // Fixing the label syntax
        fee: paymentMethodFeeMapping[paymentDevice.processorName]
          ? `${paymentMethodFeeMapping[paymentDevice.processorName]}`
          : '',
        icon: <DebitCardIcon />, // Static icon for now, modify if you want device-specific icons
        onClick: () => {
          setPaymentProcessor({
            paymentMethod: 'ATM',
            locationId: paymentDevice.posLocationId || paymentDevice.locationId,
            processorId: paymentDevice.processorId,
            processorName: paymentDevice.processorName,
            paymentDeviceConfigurationId: paymentDevice.id,
            paymentSent: false,
          });

          if (isPreTippingEnabled(paymentDevice.processorName)) {
            navigate('/tips');
          } else {
            navigate('/summary');
          }
        },
      };
    });
  };

  const paymentOptions = getPaymentOptions();

  if (paymentOptions.length === 1) {
    logger.debug(
      'Only one payment device for the selected method: ' +
        paymentOptions[0].label
    );
    logger.debug('Processor selected:' + paymentOptions[0].processorName);
    paymentOptions[0].onClick();
    return null; // Return null as there's no UI to render
  }

  return <PaymentOptionsContainer paymentOptions={paymentOptions} id="tz-atm-options" />;
};

export default AtmPayments;
