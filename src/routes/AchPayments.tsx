import { h, FunctionalComponent } from 'preact';
import { useContext } from 'preact/hooks';
import { ConfigContext } from '../AppContext';
import { Channel } from '../models';

import FfdAchPayments from '../components/channels/ffd/achPayments';
import VirtualTerminalAchPayments from '../components/channels/virtualTerminal/achPayments';
import ECommerceACHPayments from '../components/channels/ecommerce/ACHPayments';

const AchPaymentsRoute: FunctionalComponent = () => {
  const { channel } = useContext(ConfigContext);

  switch (channel) {
    case Channel.VIRTUAL_TERMINAL:
      return <VirtualTerminalAchPayments />;
    case Channel.ECOMMERCE:
      return <ECommerceACHPayments />;
    case Channel.FFD:
    default:
      return <FfdAchPayments />;
  }
};

export default AchPaymentsRoute;
