import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>ber,
  IsOptional,
  IsString,
  IsEmail,
  Min,
  ValidateNested,
  IsUUID,
  IsAlphanumeric,
  MaxLength,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';
import { Theme } from './context/ThemeProvider';
import { Navigate } from 'wouter-preact/memory-location';
import { IsOrderAmountValid } from './validators/modelValidator';
import { ParsedErrorDetails } from './utils/apiUtils';
import { MutableRefObject } from 'preact/compat';
import { IProcessorFlow } from './types/processor';

interface InfraConfigurations {
  element?: HTMLElement;
}

export enum Channel {
  FFD = 'ffd',
  ECOMMERCE = 'ecommerce',
  VIRTUAL_TERMINAL = 'virtual_terminal',
}

export enum PaymentMethod {
  CREDIT = 'credit',
  ACH = 'ach',
}

export enum ConsumerType {
  USER = 'user',
  PARTNER = 'partner',
  INTERNAL = 'internal',
}

/**
 * A model representing all possible configurations
 * that can be done from embedded script. Those settings
 * are passed around in application via Context.
 */
export interface AppConfigurations {
  authTokenFactory: () => Promise<string | undefined>;
  getEntityId: () => Promise<string | undefined>;
  sessionId: string;
  debug: boolean;
  isWebSocketEnabled: boolean;
  serviceBaseUrl: string;
  autoRenderPayments: boolean;
  disableDarkMode: boolean;
  text: {
    minimizedTitle?: string;
  };
  styles: {
    classNameContainer?: string;
  };
  payment: Payment | undefined;
  theme: Theme | undefined;
  dispensaryShortName?: string;
  channel?: Channel; // Using the enum instead of string
  consumerType?: ConsumerType;
  onPaymentLoading?: (isLoading: boolean) => void;
  onReady?: () => void; // Called when the PaySDK is ready to be used
}

export class MSOTreezPayCustomer {
  @IsNumber()
  @IsOptional()
  id: number | null;

  @IsString()
  @IsOptional()
  firstName: string;

  @IsString()
  @IsOptional()
  lastName: string;

  @IsString()
  @IsOptional()
  phone: string;

  @IsEmail({}, { message: 'Email is not valid.' })
  @IsOptional()
  email: string;
}

class Order {
  @IsArray({ message: 'Items must be an array.' })
  @ValidateNested({ each: true })
  @Type(() => Item)
  items: Item[];
  @IsNumber()
  taxAmount: number;
}

class Item {
  @IsString()
  name: string;
  @IsNumber()
  totalAmount: number;
  @IsNumber()
  quantity: number;
}

export class Payment {
  @IsString()
  @IsOptional()
  @IsUUID('4', { message: 'Payment ID must be a valid UUID version 4.' })
  paymentId?: string;

  invoiceId?: string;
  asyncUpdateReceived: boolean;
  integratedPaymentAttempts?: number;
  feeAmount?: number;

  handlePaymentResponse: (response: any) => Promise<void>;

  @ValidateNested()
  @Type(() => MSOTreezPayCustomer)
  @IsOptional() // Optional if customer is not always required
  customer?: MSOTreezPayCustomer;

  @ValidateNested()
  @Type(() => Order)
  @IsOptional() // Optional, only for Stronghold processor
  order?: Order;

  @IsNumber()
  @Min(0, { message: 'Original amount must be greater than zero.' })
  @IsOrderAmountValid({
    message: 'Order total must match the original amount.',
  })
  originalAmount: number;

  @IsUUID('4', { message: 'Payment ID must be a valid UUID version 4.' })
  @IsNotEmpty({ message: 'Ticket ID is required.' })
  ticketId: string;

  @IsAlphanumeric('en-US', {
    message: 'Ticket alphanumeric ID must contain only letters and numbers.',
  })
  @IsNotEmpty({ message: 'Ticket alphanumeric ID is required.' })
  @MaxLength(10, {
    message: 'Ticket alphanumeric ID must be at most 10 characters long.',
  })
  ticketAlphaNumId: string;

  @IsOptional()
  @IsNumber({ allowNaN: false }, { message: 'Tip must be a valid number.' })
  tip?: number;

  @IsOptional()
  @IsNumber({ allowNaN: false }, { message: 'Taxes must be a valid number.' })
  taxes?: number;

  @IsOptional()
  @IsNumber(
    { allowNaN: false },
    { message: 'Discount must be a valid number.' }
  )
  discount?: number;

  @IsOptional()
  @IsNumber(
    { allowNaN: false },
    { message: 'Reward dollars must be a valid number.' }
  )
  rewardDollars?: number;

  @IsString()
  @IsOptional()
  locationId: string | null;

  @IsString()
  @IsOptional()
  processorName?: string | null;

  @IsString()
  @IsOptional()
  @IsUUID('4', {
    message: 'Transacting AssociateId ID must be a valid UUID version 4.',
  })
  transactingAssociateId?: string;

  @IsString()
  @MaxLength(8, {
    message: 'Employee Reference Id must be at most 8 characters long.',
  })
  @IsOptional()
  employeeReferenceId?: string;

  @IsOptional()
  cancel?: boolean;

  @IsString()
  @IsOptional()
  entityName?: string | null;
}

export interface PaymentProcessor {
  paymentMethod: string;
  locationId: string | null;
  processorId: string | null;
  processorName: string;
  paymentDeviceConfigurationId: string | null;
  paymentSent: boolean;
  flow?: IProcessorFlow; // payment flow selected for the processor
}

export type Configurations = InfraConfigurations & AppConfigurations;

export interface PaymentDeviceLocation {
  id: string;
  name: string;
  deviceregisterid: string;
  deviceauthcode: string | null;
  deviceauthcodeDecrypted: string | null;
  posLocationId: string | null;
  printConfig: any | null; // Adjust type as needed
  createdAt: string; // Consider using Date type if parsing needed
  processorId: string;
  processorName: string;
  updatedAt: string; // Consider using Date type if parsing needed
  tpn: any | null; // Adjust type as needed
  locationId: string | null;
}

export interface EntityConfiguration {
  id: string;
  name: string;
  category: string | null;
  code: string;
  value: string;
  isSecret: boolean;
  isRequired: boolean;
  entityId: string;
  processorId: string;
  createdAt: string; // or Date if you want to work with Date objects
}

export interface ListPaymentDeviceLocationsResponse {
  message: string;
  data: PaymentDeviceLocation[];
}

export interface ProcessorsResponse {
  message: string;
  data: {
    processors: Processor[];
  };
}

export interface EntityConfigurationsResponse {
  message: string;
  data: {
    items: EntityConfiguration[];
  };
}

// MSOTreezPayBaseResponse interface
export interface MSOTreezPayBaseResponse<T> {
  message: string;
  isSuccess: boolean;
  data: T;
}

// MSOTreezPayConnectionStatusResponse interface
export interface MSOTreezPayConnectionStatusResponse {
  isOnline: boolean;
  raw: string;
}

export interface MSOTreezPayPaymentRequest {
  sessionId: string;
  originalAmount: number;
  feeAmount: number;
  tipAmount?: number;
  processorId: string;
  processorName: string;
  paymentDeviceConfigurationId: string;
  referenceNumber: string;
  employeeReferenceId?: string;
  transactingAssociateId?: string;
  triggerProcessor: boolean | null;
  customer?: MSOTreezPayCustomer;
  ticketId: string;
  ticketAlphaNumId: string;
}

export interface MSOTreezPayCreatePaymentResponse {
  id: string;
}

export interface MSOTreezPayCaptureChargeRequest {
  ticketId?: string;
  ticketAlphaNumId?: string;
  feeAmount?: number;
  referenceNumber?: string;
  processorName: string;
  originalAmount?: number;
  employeeReferenceId?: string;
  isSync: boolean;
  customer?: MSOTreezPayCustomer;
  transactingAssociateId?: string;
  paymentChargeId?: string;
}

export interface MSOTreezPayCaptureChargeResponse {
  id: string;
  originalAmount: number;
  tipAmount: number;
  feeAmount: number;
  cashbackAmount: number;
  createdAt: string;
  updatedAt: string;
  method: string;
  status: string;
  customer?: MSOTreezPayCustomer;
  transactingAssociateId?: string;
  ticketId?: string;
  ticketAlphaNumId?: string;
  employeeReferenceId?: string;
  referenceNumber?: string;
  lastActionTaken: string;
  entityId: string;
  integrationType: string;
  processorId: string;
  processorName: string;
  processorPaymentId: string;
}

export interface MSOTreezPayCancelPaymentResponse {
  canceled: boolean;
}

interface PaymentLinkUrl {
  message: string;
  url: string;
}

interface InvoiceData {
  id: string;
  paymentLinkUrls: PaymentLinkUrl[];
}

export interface CreateInvoiceResponse {
  message: string;
  data: InvoiceData;
  errorRaw: any;
}

export interface Preview {
  totalAmount: number;
  feeAmount: number;
  tipAmount: number;
  taxAmount: number;
  cashbackAmount: number;
  roundAmount: number;
}

export interface PreviewResponse {
  message: string;
  data: Preview;
  errorRaw: any;
}

interface PaymentOption {
  method: string;
  processor: string;
}

interface OrderItem {
  name: string;
  totalAmount: number;
  quantity: number;
}

interface Order {
  items: OrderItem[];
}

interface Callbacks {
  successUrl: string;
  exitUrl: string;
}

export interface InvoiceRequest {
  associateId?: string;
  sessionId: string;
  method: string;
  type: string;
  senderPhone?: string;
  senderEmail?: string;
  senderFirstName?: string;
  senderLastName?: string;
  customer?: MSOTreezPayCustomer;
  ticketAlphaNumId: string;
  referenceNumber: string; // alphaNum ID
  originalAmount: number;
  taxAmount: number;
  feeAmount?: number;
  paymentOptions: PaymentOption[];
  order: Order;
  callbacks?: Callbacks;
}

export interface PreviewRequest {
  processorName: string;
  totalAmount: number;
  tipAmount: number;
}

export type Configuration = {
  id: string;
  name: string;
  category: string | null;
  code: string;
  value: string;
  isSecret: boolean;
};

export type Processor = {
  processorId: string;
  processorName: string;
  paymentMethod: string;
  configurations: Configuration[];
  flows?: IProcessorFlow[];
};

export interface WidgetApi {
  getProcessors: (channel: Channel) => Promise<ProcessorsResponse>;
  getEntityConfigurations: () => Promise<EntityConfigurationsResponse>;
  getPaymentDeviceLocations: (
    processorNames: string[]
  ) => Promise<ListPaymentDeviceLocationsResponse>;
  getPaymentDeviceStatus: (
    paymentDeviceLocationId: string
  ) => Promise<MSOTreezPayBaseResponse<MSOTreezPayConnectionStatusResponse>>;
  getPreview: (previewRequest: PreviewRequest) => Promise<PreviewResponse>;
  createPayment: (
    paymentRequest: MSOTreezPayPaymentRequest
  ) => Promise<MSOTreezPayBaseResponse<MSOTreezPayCreatePaymentResponse>>;
  cancelPayment: (
    paymentId: string
  ) => Promise<MSOTreezPayBaseResponse<MSOTreezPayCancelPaymentResponse>>;
  createInvoice: (
    paymentRequest: InvoiceRequest
  ) => Promise<CreateInvoiceResponse>;
  getPaymentStatus: (
    paymentId: string
  ) => Promise<MSOTreezPayBaseResponse<MSOTreezPayPaymentStatusResponse>>;
  getInvoiceStatus: (
    invoiceId: string
  ) => Promise<MSOTreezPayBaseResponse<MSOTreezPayInvoiceStatusResponse>>;
  captureCharge: (
    captureChargeRequest: MSOTreezPayCaptureChargeRequest
  ) => Promise<MSOTreezPayBaseResponse<MSOTreezPayCaptureChargeResponse>>;
}

export interface Globals {
  widgetOpen: boolean;
  setWidgetOpen: (open: boolean) => void;
  payment: Payment | undefined;
  setPayment: (payment?: Payment | ((prevPayment: Payment) => Payment)) => void;
  paymentProcessor: PaymentProcessor | undefined;
  setPaymentProcessor: (paymentProcessor?: PaymentProcessor) => void;
  overlayVisible: boolean;
  setOverlayVisible: (visible: boolean | ((prev: boolean) => boolean)) => void;
  overlayMessage: string;
  setOverlayMessage: (message: string) => void;
  setOverlayContext: (context: any) => void;
  overlayContext: Record<string, any>;
  resetOverlay: () => void;
  memoryLocationHook: any;
  navigate: Navigate;
  history: any;
  currentPath: string;
  element?: HTMLElement;
  apiError?: ParsedErrorDetails | null;
  setApiError: (ParsedErrorDetails: any) => void;
  showTotalDetails: boolean;
  setShowTotalDetails: (
    expanded: boolean | ((prev: boolean) => boolean)
  ) => void;
  newTabRef: MutableRefObject<{ tab: Window | null; url?: string | null }>;
  setMountedComponents: React.Dispatch<
    React.SetStateAction<Record<string, MountableComponentConfig>>
  >;
}

export interface MountableComponentConfig {
  component: string;
  element?: HTMLElement;
}

export enum MSOTreezPayPaymentStatus {
  UNDEFINED = '',
  PENDING = 'pending',
  STARTED = 'started',
  APPROVED = 'approved',
  SUCCESS = 'success',
  CANCELED = 'canceled',
  DECLINED = 'declined',
  NOT_FOUND = 'not found',
  ERROR = 'error',
  COMPLETED = 'completed',
  AUTHORIZED = 'authorized',
  REFUNDED = 'refunded',
  TERMINAL_BUSY = 'terminal busy',
  TERMINAL_ERROR = 'terminal error',
  VOIDED = 'voided',
}

export interface MSOTreezPayPaymentStatusResponse {
  id: string;
  originalAmount: number;
  feeAmount: number;
  tipAmount: number;
  cashbackAmount: number;
  taxAmount: number;
  referenceNumber: string;
  ticketAlphaNumId: string;
  processorResponseMessage: string;
  processorResponseCode: string;
  processorResponseDetailMessage: string;
  status: MSOTreezPayPaymentStatus;
  displayErrorMessage: string;
  isAsync: boolean;
  method: string;
  paymentSource: string;
  processorName: string;
  isPaymentCreation: boolean;
  processorId: string;
  updatedAt: string;
}

export enum MSOTreezPayInvoiceStatus {
  UNDEFINED = '',
  NEW = 'new',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  CANCELED = 'canceled',
  DECLINED = 'declined',
  NOT_FOUND = 'not found',
  ERROR = 'error',
}

export interface MSOTreezPayInvoiceStatusResponse {
  id: string;
  invoiceId: string;
  processorId: string | null;
  processorName?: string;
  entityId: string | null;
  originalAmount: number;
  cashbackAmount?: number;
  method: string | null;
  ticketAlphaNumId: string | null;
  referenceNumber: string | null;
  taxAmount: number;
  tipAmount: number;
  feeAmount: number;
  status: MSOTreezPayInvoiceStatus;
  chargeId: string;
  createdDate: Date;
  paymentLinkUrl: string;
  isAuthorized: boolean;
  updatedAt: string;
}
