import * as Sentry from '@sentry/react';

const isSandbox = process.env.SANDBOX === 'true';
const isPreprod = process.env.PREPROD === 'true';
const isSentryEnabled = process.env.SENTRY_ENABLED === 'true';
const isLocal = process.env.LOCAL_ENV === 'true';
const environment = isSandbox || isPreprod
  ? 'development': 'production';

Sentry.init({
  enabled: !isLocal && isSentryEnabled,
  dsn: process.env.SENTRY_DSN,
  integrations: [
     Sentry.replayIntegration(),
  ],
  tracePropagationTargets: [],

  // Capture replay 100% of sessions with an error on prod
  replaysOnErrorSampleRate: isSandbox || isPreprod ? 0: 1.0,

  environment,

  beforeSend(event) {
    return event;
  },
});
