import { h, Fragment } from 'preact';
import Progress from '../../routes/Progress';
import { useContext, useState } from 'preact/hooks';
import { GlobalsContext } from '../../AppContext';
import { DeviceContext } from '../../context/DeviceProvider';
import useLogger from '../../hooks/useLogger';
import { getProcessorIcon } from '../../utils/processorIcons';
import { useGeneratePaymentLink } from '../../hooks/useGeneratePaymentLink';
import {
  PaymentErrorEvent,
  PaymentLinkCreatedEvent,
} from '../../events/PaymentEvents';
import { usePaymentStatusPolling } from '../../components/handlers/PaymentStatusPollingHandler';
import useResetPayment from '../../hooks/useResetPayment';

export interface IAchPaymentOptions {
  id: string;
  name: string;
  isActive: boolean;
  fee: string;
  icon: any;
  onClick: () => Promise<void>;
}

interface AchPaymentsAdapterProps {
  renderPaymentOptions: (options: IAchPaymentOptions[]) => JSX.Element;
  renderOverlay?: {
    onGeneratingPaylink?: () => JSX.Element;
    onPaylinkCreated?: (processorName: string) => JSX.Element;
    onError?: (error: any) => JSX.Element;
  };
  openInNewTab?: boolean;
  autoSelectSingleProcessor?: boolean;
}

const AchPaymentsAdapter = ({
  renderPaymentOptions,
  renderOverlay = {},
  openInNewTab = false,
  autoSelectSingleProcessor = true,
}: AchPaymentsAdapterProps) => {
  const logger = useLogger();
  const [isProcessorSelected, setIsProcessorSelected] = useState(false);

  const {
    payment,
    setPayment,
    navigate,
    setOverlayVisible,
    setOverlayMessage,
    setOverlayContext,
    newTabRef,
  } = useContext(GlobalsContext);
  const deviceContext = useContext(DeviceContext);
  const { generatePaymentLink, getFeeAmount } = useGeneratePaymentLink();
  const { stopPolling, startPolling } = usePaymentStatusPolling();

  if (!deviceContext) {
    return <div>Loading...</div>;
  }

  if (!payment) {
    setOverlayVisible(true);
    setOverlayMessage(`Waiting for Payment Information`);
    setTimeout(() => {
      navigate('/');
      setOverlayVisible(false);
    }, 2000);
    return <div>Waiting for Payment Information</div>;
  }

  const { handlePaymentResponse } = payment;
  const { paymentMethods, paymentMethodFeeMapping } = deviceContext;
  const [submitting] = useState(false);

  const handlePaymentLink = async (processorName: string) => {
    try {
      if (renderOverlay?.onGeneratingPaylink) {
        setOverlayVisible(true);
        setOverlayContext({
          customComponent: renderOverlay?.onGeneratingPaylink?.(),
        });
      }

      stopPolling();

      const result = await generatePaymentLink(processorName, 'ACH');

      if (renderOverlay?.onPaylinkCreated) {
        setOverlayVisible(true);
        setOverlayContext({
          customComponent: renderOverlay?.onPaylinkCreated?.(processorName),
        });
      } else {
        setOverlayVisible(false);
        setOverlayContext(null);
      }

      const feeAmount = getFeeAmount(processorName, paymentMethodFeeMapping);

      setPayment(prevPayment => ({
        ...prevPayment,
        invoiceId: result?.data.id,
        asyncUpdateReceived: false,
        cancel: false,
        feeAmount,
      }));

      const url = result?.data.paymentLinkUrls[0]?.url;

      if (!url) {
        throw new Error('Payment link not found');
      }

      const createdEvent: PaymentLinkCreatedEvent = {
        eventType: 'PAYMENT_LINK_CREATED',
        userMessage: 'Payment link created',
        processorName,
        paymentMethod:
          deviceContext.getPaymentMethodByProcessorName(processorName) || 'ACH',
        data: {
          paymentLink: url,
        },
      };

      handlePaymentResponse(createdEvent);

      if (result?.data.id) {
        startPolling(result?.data.id, 'invoice');
      }

      // Handle navigation
      if (openInNewTab) {
        newTabRef.current.tab = window.open(url, '_blank');
        newTabRef.current.url = url;
      } else {
        navigate(`/qr/${url}`);
      }
    } catch (error) {
      logger.error('Payment link generation error:', error);

      const errorEvent: PaymentErrorEvent = {
        eventType: 'PAYMENT_ERROR',
        userMessage: 'Error creating payment. See logs.',
        processorName,
        paymentMethod:
          deviceContext.getPaymentMethodByProcessorName(processorName) || 'ACH',
        data: {},
      };

      handlePaymentResponse(errorEvent);
      if (renderOverlay?.onError) {
        setOverlayVisible(true);
        setOverlayContext({
          customComponent: renderOverlay?.onError(error),
        });
      } else {
        setOverlayVisible(false);
        setOverlayContext(null);
      }
    }
  };

  const getPaymentOptions = () => {
    const options = paymentMethods['ACH'].map((processorName: string) => ({
      name: processorName,
      isActive: true,
      fee: paymentMethodFeeMapping[processorName]
        ? `${paymentMethodFeeMapping[processorName]}`
        : '',
      icon: getProcessorIcon(processorName.toLowerCase()),
      onClick: () => handlePaymentLink(processorName),
      id: `${processorName.replace(/\s+/g, '-').toLowerCase()}-option`,
    }));

    return options;
  };

  const paymentOptions = getPaymentOptions();

  if (
    paymentOptions.length === 1 &&
    !isProcessorSelected &&
    autoSelectSingleProcessor
  ) {
    logger.debug(
      'Only one payment device for the selected method: ' +
        paymentOptions[0].name
    );
    paymentOptions[0].onClick();
    setIsProcessorSelected(true);
    return null; // Return null as there's no UI to render
  }

  return (
    <Fragment>
      {submitting && (
        <Progress
          id="ach-progress"
          height="3px"
          color="#6cc644"
          indeterminate={true}
        />
      )}
      <div id="tz-ach-options">{renderPaymentOptions(paymentOptions)}</div>
    </Fragment>
  );
};

export default AchPaymentsAdapter;
