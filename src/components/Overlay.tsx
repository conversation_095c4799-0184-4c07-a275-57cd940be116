import { h, FunctionComponent } from 'preact';
import { useContext, type CSSProperties } from 'preact/compat'; // Add this
import clsx from 'clsx';
import styles from './overlay.css';
import { ConfigContext } from '../AppContext';
import { Channel } from '../models';

interface OverlayProps {
  message?: string;
  bg?: string;
  icon?: string;
  context?: {
    customComponent?: JSX.Element;
    isProgress?: boolean;
    disableButtonsOnProgress?: boolean;
    darkBackground?: boolean;
    customButtons?: Array<{
      label: string; // Button label text
      onClick: () => void; // Click handler for the button
      style?: CSSProperties; // Optional styling for the button
      icon?: string; // Optional icon for the button
    }>;
  };
  onCancel?: () => void; // Add onCancel callback prop
}

const Overlay: FunctionComponent<OverlayProps> = ({
  message,
  bg,
  icon,
  context,
  onCancel,
}) => {
  const config = useContext(ConfigContext);

  if (context?.darkBackground) {
    return <div className={clsx(styles.container, styles.darkBg)} />;
  }

  const channelThemeStyles = {
    [Channel.VIRTUAL_TERMINAL]: {
      container: styles.vtContainer,
    },
    [Channel.FFD]: {},
    [Channel.ECOMMERCE]: {
      container: styles.ecommContainer,
    },
  }[config?.channel || Channel.FFD];

  return (
    <div
      className={clsx(
        styles.container,
        styles.lightBg,
        channelThemeStyles?.container
      )}
      id="tz-overlay"
    >
      {icon && <img src={icon} alt="Overlay Icon" className={styles.icon} />}
      {context?.customComponent ? (
        context.customComponent
      ) : (
        <div className={styles.message}>
          {message || 'Connecting to the Terminal'}
        </div>
      )}
      <div className={styles.buttonContainer}>
        {context?.customButtons &&
          context.customButtons.length > 0 &&
          !context.disableButtonsOnProgress &&
          context.customButtons.map((button, index) => (
            <button
              key={index}
              className={styles.button}
              style={button.style}
              onClick={button.onClick}
            >
              {button.icon && (
                <img src={button.icon} alt="" className={styles.buttonIcon} />
              )}
              {button.label}
            </button>
          ))}
      </div>
    </div>
  );
};

export default Overlay;
