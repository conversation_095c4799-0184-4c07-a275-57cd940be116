.container {
  display: flex;
  padding: 16px 44px 16px 32px;
  justify-content: center;
  align-items: flex-start;
  gap: 16px;
  border-radius: 16px 16px 0px 0px;
  background: #F3FAE6;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  min-width: 640px;
}

.header {
  color: #595959;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
}

.inlineContainer {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 24px;
}

.blockContainer {
  flex-direction: column;
}

.list {
  display: flex;
  gap: 24px;
  align-items: center;
}

.listItem {
  display: flex;
  align-items: center;
  gap: 8px;
}

.optionText {
  color:  #1A4007;
  font-size: 20px;
  font-weight: 450;
  line-height: 28px;
}

.optionIcon {
  padding: 5px;
  border-radius: 8px;
  background: #DBF5B3;
  display: flex;
  align-items: center;
  justify-content: center;
}

.optionIcon > img {
  width: 24px;
  height: 24px;
}

.stretchList {
  justify-content: space-between;
  width: 100%;
}

.roundedDebitIcon, .cardIcon  {
  width: 27px;
  height: 27px;
}

/** circle */
.roundedDebitIcon path:first-child {
  fill: #295C07;
  stroke: #295C07;
  stroke-width: 1.5;
}

/** arrow */
.roundedDebitIcon path:nth-child(2) {
  stroke: #295C07;
  stroke-width: 3.5;
}

/** $ */
.roundedDebitIcon path:last-child {
  fill: #295C07;
}

.cardIcon path {
  fill: #295C07;
  stroke: #295C07;
  stroke-width: 0.2;
}

.container::before {
  content: "";
  position: absolute;
  top: 0;
  left: -10px;
  width: 3px;
  height: 100%;
  transform: rotate(24deg);
  background: rgba(140, 204, 82, 0.35);
  box-shadow: 1px 1px 4px 1px rgba(140, 204, 82, 0.15);
  filter: blur(3.5px);
  animation: glare-animation 4s infinite linear;
}

@keyframes glare-animation {
  0% {
    left: -10px;
  }
  100% {
    left: 100%;
  }
}
