import { Fragment, h } from 'preact';
import { useContext } from 'preact/hooks';
import clsx from 'clsx';
import { DeviceContext } from '../../context/DeviceProvider';
import RoundedDebitIcon from '../../components/icons/roundedDebit';
import CardIcon from '../../components/icons/card';
import achIcon from '../../assets/mobile.svg';
import styles from './paymentOptionsBanner.css';
import rootStyles from '../../layout/styles/global.css';

const PaymentOptionsBanner = () => {
  const deviceContext = useContext(DeviceContext);

  if (!deviceContext || deviceContext.loading) {
    return <Fragment />;
  }

  const { paymentMethods } = deviceContext;

  const paymentCount = Object.keys(paymentMethods).length;
  const containerClass = clsx(
    styles.container,
    paymentCount <= 2 && styles.inlineContainer,
    paymentCount > 2 && styles.blockContainer
  );

  const styleConfig = {
    Debit: {
      label: 'Debit',
      icon: <CardIcon className={styles.cardIcon} />,
    },
    ATM: {
      label: 'Rounded Debit',
      icon: <RoundedDebitIcon className={styles.roundedDebitIcon} />,
    },
    ACH: {
      label: 'ACH',
      icon: <img src={achIcon} alt="ACH Icon" className={styles.achIcon} />,
    },
    Credit: {
      label: 'Credit',
      icon: <CardIcon className={styles.cardIcon} />,
    },
  };

  return (
    <div
      id="tz-payment-methods-banner"
      className={clsx(containerClass, rootStyles.reset)}
    >
      <span className={styles.header}>Payment types accepted</span>
      <div
        className={clsx(styles.list, paymentCount > 2 && styles.stretchList)}
      >
        {Object.keys(paymentMethods).map(methodName => (
          <div key={methodName} className={styles.listItem}>
            <span className={styles.optionIcon}>
              {styleConfig[methodName].icon}
            </span>
            <span className={styles.optionText}>
              {styleConfig[methodName].label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PaymentOptionsBanner;
