import { h, FunctionComponent, Fragment } from 'preact';
import { createPortal } from 'preact/compat';
import PaymentOptionsBanner from './paymentOptionsBanner';
import { MountableComponentConfig } from '../../models';

interface MountableComponentsRendererProps {
  mountedComponents: Record<string, MountableComponentConfig>;
}

const elementMap = {
  'payment-options-banner': <PaymentOptionsBanner />,
};

const MountableComponentsRenderer: FunctionComponent<
  MountableComponentsRendererProps
> = ({ mountedComponents }) => {
  return (
    <Fragment>
      {Object.entries(mountedComponents).map(([_, { component, element }]) =>
        createPortal(elementMap[component], element || document.body)
      )}
    </Fragment>
  );
};

export default MountableComponentsRenderer;
