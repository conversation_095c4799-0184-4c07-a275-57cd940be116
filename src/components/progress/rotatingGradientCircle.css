.skillContainer {
  position: relative;
  margin: auto;
}

.outer {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  padding: 5%;
}

.inner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.number {
  font-weight: 600;
  color: #555;
  font-size: 2em;
}

.svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.circle {
  fill: none;
  stroke: url(#GradientColor);
  stroke-width: 20;
  stroke-dasharray: 694.29 373.85;
  stroke-dashoffset: 0;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
  animation: rotateCircle 2s linear infinite;
}

.wrapper {
  position: relative;
}

@keyframes rotateCircle {
  from {
    transform: rotate(-90deg);
  }
  to {
    transform: rotate(270deg);
  }
}
