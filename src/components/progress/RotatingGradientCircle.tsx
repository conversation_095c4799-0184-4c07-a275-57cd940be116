import { h } from 'preact';
import styles from './rotatingGradientCircle.css';
import clsx from 'clsx';

interface RotatingGradientBaseProps {
  gradientColors: { stop1: string; stop2: string; stop3: string };
  size: string;
  maxSize: string;
  icon?: string;
  className?: string;
}

const RotatingGradientCircle = ({
  gradientColors,
  size,
  maxSize,
  icon,
  className = '',
}: RotatingGradientBaseProps) => {
  return (
    <div
      className={clsx(styles.wrapper, className)}
      id="tz-rotating-gradient-circle"
    >
      <div
        className={styles.skillContainer}
        style={{
          width: size,
          height: size,
          maxHeight: maxSize,
          maxWidth: maxSize,
        }}
      >
        <div className={styles.outer}>
          <div className={styles.inner}>
            {icon && <img className={styles.number} src={icon} />}
          </div>
        </div>
        <svg viewBox="0 0 360 360" className={styles.svg}>
          <defs>
            <linearGradient id="GradientColor" gradientUnits="userSpaceOnUse">
              <stop
                offset="0%"
                stop-color={gradientColors.stop1}
                stop-opacity="1"
              />
              <stop
                offset="65%"
                stop-color={gradientColors.stop2}
                stop-opacity="1"
              />
              <stop
                offset="100%"
                stop-color={gradientColors.stop3}
                stop-opacity="0"
              />
            </linearGradient>
          </defs>
          <circle
            cx="180"
            cy="180"
            r="170"
            stroke-linecap="round"
            className={styles.circle}
          />
        </svg>
      </div>
    </div>
  );
};

export default RotatingGradientCircle;
