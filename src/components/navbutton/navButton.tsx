import { FunctionalComponent, h } from 'preact';
import styles from './navbutton.css';
import { useContext } from 'preact/hooks';
import { ConfigContext } from '../../AppContext';
import { Channel } from '../../models';
import clsx from 'clsx';

interface NavButtonProps {
  onClick: () => void;
  icon: any;
}

const NavButton: FunctionalComponent<NavButtonProps> = ({ onClick, icon }) => {
  const { channel } = useContext(ConfigContext);

  const channelCls = {
    [Channel.VIRTUAL_TERMINAL]: styles.virtualTerminal,
    [Channel.FFD]: styles.ffd,
    [Channel.ECOMMERCE]: '',
  }?.[channel ?? Channel.FFD];

  return (
    <div className={clsx(styles.container, channelCls)}>
      <div role="button" onClick={() => onClick()} className={styles.button}>
        <img src={icon} alt={icon.toString()} />
      </div>
    </div>
  );
};

export default NavButton;
