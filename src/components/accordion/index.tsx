import { h } from 'preact';
import { useState } from 'preact/hooks';
import styles from './accordion.css';
import { ChevronUp, ChevronDown } from '../icons/chevron';
import clsx from 'clsx';

interface IBaseAccordionProps {
  title: (isOpen: boolean) => h.JSX.Element;
  onExpand: h.JSX.Element;
}

interface IAccordionProps extends IBaseAccordionProps {
  isOpen: boolean;
}

interface IAccordionItemProps extends IAccordionProps {
  onClick?: () => void;
  nonCollapsible?: boolean;
}

interface IAccordionGroupProps {
  items: IBaseAccordionProps[];
}

export const Accordion = ({
  title,
  onExpand,
  isOpen,
  onClick,
  nonCollapsible = false,
}: IAccordionItemProps) => {
  return (
    <div className={clsx(styles.container, isOpen && styles.accordionOpen)}>
      <button className={styles.header} onClick={onClick}>
        {title(isOpen)}
        <span>
          {!nonCollapsible && (isOpen ? <ChevronUp /> : <ChevronDown />)}
        </span>
      </button>
      <div className={`${styles.expand} ${isOpen ? styles.expandOpen : ''}`}>
        <div className={styles.body}>{onExpand}</div>
      </div>
    </div>
  );
};

const AccordionGroup = ({ items }: IAccordionGroupProps) => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const handleToggle = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className={styles.group}>
      {items.map((item, index) => (
        <Accordion
          key={index}
          title={item.title}
          onExpand={item.onExpand}
          isOpen={openIndex === index}
          onClick={() => handleToggle(index)}
        />
      ))}
    </div>
  );
};

export default AccordionGroup;
