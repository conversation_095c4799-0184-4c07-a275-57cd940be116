.wrapper {
  overflow-y: auto;
}

.containerPaddingBtm {
  padding-bottom: var(--tz-footer-height);
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tipButton, input.tipButton {
  height: 146px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 32px;
  border-radius: 24px;
  border: 1px solid #E6E6E6;
  background: #FFF;
  box-shadow: 0px 1px 2px -1px rgba(0, 0, 0, 0.10), 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
  color: var(--tz-text-color-primary);
  font-size: 36px;
  font-weight: 500;
  line-height: 120%;
  outline: none;
  flex-direction: column;
}

.tipCalculatedAmount {
  color: #666E7A;
  font-size: 24px;
  font-weight: 600;
  line-height: 28.8px;
  margin-top: 8px;
}

.tipButton:focus, .tipButton:focus-visible,
input.tipButton:focus, input.tipButton:focus-visible {
  border: 3px solid #A1E963;
  outline: none;
}

.tipOptions {
  width: 100%;
  grid-gap: 32px;
}

.ctaContainer {
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  height: var(--tz-footer-height);
  padding: 24px var(--tz-padding-x);
  border-top: 1px solid #CCCFD3;
  background: #FFF;
}

.ctaFixed {
  position: fixed;
}

.ctaAbsolute {
  position: absolute;
}

.selectedTipButton {
  background: #F7FCEE;
  border: 3px solid #A1E963;
}

input.customTipInput {
  width: auto;
}

input.customTipInput::-webkit-outer-spin-button,
input.customTipInput::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input.customTipActive {
  outline: none;
  border: 3px solid #A1E963;
  box-shadow: none;
}

input.customTipInputSelected {
  background: #F7FCEE;
}

/* landscape mode */
@media (min-aspect-ratio: 1/1) {
  .tipButton {
    grid-column: span 2;
  }

  .tipOptions {
    margin: 40px 0;
    display: grid;
    grid-template-columns: repeat(6, 1fr);
  }

  .tipButton:last-child:nth-child(3n - 1) {
    grid-column-end: -2;
  }

  .tipButton:nth-last-child(2):nth-child(3n + 1) {
    grid-column-end: 4;
  }
}

/* portrait mode */
@media (max-aspect-ratio: 1/1) {
  .tipButton {
    grid-column: span 2;
  }

  .tipOptions {
    margin: 32px 0;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
  }

  .tipButton:last-child:nth-child(3n - 1) {
    grid-column-end: -2;
  }
}
