import { FunctionalComponent, h } from 'preact';
import { useCallback, useContext, useEffect, useRef, useState } from 'preact/hooks';
import { memo } from 'preact/compat';
import CustomPaymentHeader from '../payments/CustomPaymentHeader';
import { GlobalsContext } from '../../AppContext';
import RightArrow from '../../assets/ArrowRight.svg';
import Button from '../buttons/button';
import { PreviewContext } from '../../context/PreviewProvider';
import { dollarsToCents, toDollars } from '../../utils/currencyUtils';
import clsx from 'clsx';
import styles from './tip.css';
import useDebounce from '../../hooks/useDebounce';
import { useTheme } from '../../context/ThemeProvider';

interface TipComponentProps {
  tipsVariants: number[];
  handleBack: () => void;
  onSubmit: (tip: number | string) => void;
}

const TipComponent: FunctionalComponent<TipComponentProps> = ({
  tipsVariants,
  handleBack,
  onSubmit,
}) => {
  const previewContext = useContext(PreviewContext);
  const { payment, paymentProcessor } = useContext(GlobalsContext);
  const [customTip, setCustomTip] = useState<string>('$');
  const [selectedTipPercent, setSelectedTipPercent] = useState<number | null>(null);
  const [isCustomInputActive, setCustomInputActive] = useState<boolean>(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const debouncedCustomTip = useDebounce<string>(customTip, 650);
  const { theme } = useTheme();

  const resetCustomTip = () => setCustomTip('$');

  const resetState = () => {
    resetCustomTip();
    setSelectedTipPercent(null);
    setCustomInputActive(false);
  };

  const handleSubmit = useCallback(() => {
    const tip = Number(selectedTipPercent !== null ? getPercentTipAmount(selectedTipPercent) : customTip?.slice(1));
    if (tip >= 0) {
      onSubmit(tip);
      resetState();
    }
  }, [selectedTipPercent, customTip, onSubmit, resetState]);

  useEffect(() => {
    const _customTip = debouncedCustomTip?.slice(1)
    if(_customTip !== '')
      handleTipSelection(_customTip);
  }, [debouncedCustomTip]);

  const handleCustomTipSelection = () => {
    setSelectedTipPercent(null);
    setCustomInputActive(true);

    if (inputRef.current) {
      inputRef.current.focus();
      inputRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' }); // Scroll to the custom tip button when it's activated

      if (inputRef.current.selectionStart === 0) {
        inputRef?.current?.setSelectionRange && inputRef?.current?.setSelectionRange(1,1); // place cursor after $
      }
    }
  };

  const handleTipSelection = useCallback(
    (tip: number | string) => {
      if (previewContext && paymentProcessor?.processorName) {
        previewContext.fetchPreviewPaymentTotals(
          paymentProcessor.processorName,
          tip ? Number(dollarsToCents(tip)) : 0
        );
      }
    },
    [previewContext, paymentProcessor]
  );

  const handleCustomTipInput = (e: Event) => {
    const currentInput = e.currentTarget as HTMLInputElement;
    const value = currentInput.value;

    /**
     * $ currency between 1.00 and 999.99
     * Optional: up to 2 digits after the decimal point.
     * Rejects < $1
     */
    let validTip = (value === '$') || /^\d{1,3}(\.\d{0,2})?$/.test(value.slice(1));
    validTip = validTip && !value.startsWith('$0') && !value.startsWith('$.');

    if (validTip) {
      setCustomTip(value);
    } else {
      if (inputRef.current) {
        // restore input
        const start = inputRef.current.selectionStart ?? 0;
        const end = inputRef.current.selectionEnd ?? 0;
        const diffLength = Math.abs(currentInput.value.length - customTip.length);
        inputRef.current.value = customTip;
        inputRef.current.setSelectionRange(start - diffLength, end - diffLength);
      }
    }
  };

  const handleCustomTipBlur = () => {
    if (!customTip || customTip === '$') {
      setCustomInputActive(false);
    }
    else if (customTip.endsWith('.')) {
      // append '00' to make it a valid decimal
      setCustomTip(prev => `${prev}00`);
    }
  };

  const handleCustomTipKeyDown = (e: h.JSX.TargetedKeyboardEvent<HTMLInputElement>) => {
    if (isCustomInputActive) {
      // prevent cursor from moving before $
      if ((e.key === 'ArrowUp' || e.key === 'ArrowLeft') && inputRef.current?.selectionStart === 1) {
        e.preventDefault();
      }
    } else {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleCustomTipSelection();
      }
    }
  }

  const getPercentTipAmount = (percent: number) => {
    const totalAmount = payment?.originalAmount || 0;
    return toDollars((percent / 100) * totalAmount);
  };

  const isCustomTipUpdated = Boolean(customTip) && dollarsToCents(customTip?.slice(1)) === previewContext?.previewTotals?.tipAmount?.toString();
  const isCheckoutDisabled = previewContext?.loading || (selectedTipPercent === null && !isCustomTipUpdated);

  return (
    <div className={clsx(styles.wrapper, !theme.positionFixed && styles.containerPaddingBtm)} id="tz-tip">
      <div className={styles.container}>
        <CustomPaymentHeader
          headingText="Would you like to leave a tip?"
          paragraphText={`Show your support for the team${payment?.entityName ? ` at ${payment?.entityName}` : ""}`}
        />
        <div className={styles.tipOptions}>
          {tipsVariants.map((tipPercent, index) => {
            const tipPercentValue = getPercentTipAmount(tipPercent);

            return (
              <div
                key={index}
                role="button"
                tabIndex={0}
                className={clsx(styles.tipButton, {
                  [styles.selectedTipButton]: selectedTipPercent === tipPercent,
                })}
                onClick={() => {
                  resetCustomTip();
                  setCustomInputActive(false);
                  setSelectedTipPercent(tipPercent);

                  handleTipSelection(tipPercentValue);
                }}
                onKeyDown={e =>
                  e.key === 'Enter' && setSelectedTipPercent(tipPercent)
                }
              >
                <div>{tipPercent === 0 ? 'No Tip' : `${tipPercent}%`}</div>
                {tipPercent ? (
                  <div className={styles.tipCalculatedAmount}>
                    ${tipPercentValue}
                  </div>
                ) : (
                  ''
                )}
              </div>
            );
          })}
          <input
            ref={inputRef}
            type="text"
            tabIndex={0}
            value={isCustomInputActive ? customTip : 'Custom'}
            className={clsx(styles.tipButton, styles.customTipInput, {
              [styles.customTipInputSelected]: isCustomTipUpdated,
            },
              isCustomInputActive ? styles.customTipActive : styles.customTipReadOnly,
            )}
            placeholder=""
            onBlur={handleCustomTipBlur}
            maxLength={7}
            onInput={handleCustomTipInput}
            inputMode="decimal"
            onClick={handleCustomTipSelection}
            onKeyDown={handleCustomTipKeyDown}
          />
        </div>
      </div>
      <div className={clsx(styles.ctaContainer, !theme.positionFixed ? styles.ctaFixed: styles.ctaAbsolute)}>
        <Button
          disabled={false}
          onClick={handleBack}
          text="Back"
          variant="secondary"
        />
        <Button
          disabled={isCheckoutDisabled}
          onClick={handleSubmit}
          text="Checkout"
          variant="primary"
          icon={RightArrow}
        />
      </div>
    </div>
  );
};

export default memo(TipComponent);
