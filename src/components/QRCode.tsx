import { h } from 'preact';
import { useContext, useEffect, useRef } from 'preact/hooks';
import QRCode, { QRCodeRenderersOptions } from 'qrcode';
import { useRoute } from 'wouter-preact';
import loader from '../assets/achLoading.gif';
import { toDollars } from '../utils/currencyUtils';
import styles from './QRCode.css';
import clsx from 'clsx';
import { ConfigContext, GlobalsContext } from '../AppContext';
import { DeviceContext } from '../context/DeviceProvider';
import Button from './buttons/button';
import useLogger from '../hooks/useLogger';
import useNavigateBack from '../hooks/useNavigateBack';
import { Channel } from '../models';
import useResetPayment from '../hooks/useResetPayment';

type QRCodeChannels = Extract<Channel, Channel.FFD | Channel.VIRTUAL_TERMINAL>;

const QRCodeComponent = () => {
  const logger = useLogger();
  const navigateBack = useNavigateBack();
  const resetPayment = useResetPayment();

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [_match, params] = useRoute('/qr/*');

  if (!params) {
    return <div>No Pay Link</div>;
  }

  const { payment, navigate } = useContext(GlobalsContext);
  const { channel } = useContext(ConfigContext);
  const deviceContext = useContext(DeviceContext);
  const { paymentMethods } = deviceContext || {};
  const achProviders = paymentMethods?.['ACH'] || [];
  const options = achProviders;

  const payLink = params['0'];

  const themeConfig: Record<
    QRCodeChannels,
    {
      colors: { dark: string; light: string };
      container?: string;
      canvas?: string;
      loader?: string;
    }
  > = {
    [Channel.FFD]: {
      colors: { dark: '#1A4007', light: '#FCFFF5' },
    },
    [Channel.VIRTUAL_TERMINAL]: {
      colors: { dark: '#000', light: '#FCFFF5' },
      container: styles.vtContainer,
      canvas: styles.vtCanvas,
      loader: styles.vtLoader,
    },
  };
  const qrChannel =
    channel === Channel.FFD || channel === Channel.VIRTUAL_TERMINAL
      ? channel
      : Channel.FFD;

  const theme = themeConfig[qrChannel];

  const buttonConfig: Record<
    QRCodeChannels,
    {
      variant: string;
      text: string;
      onClick: () => void;
    }
  > = {
    [Channel.FFD]: {
      variant: 'danger',
      text: 'Cancel',
      onClick: () => navigateBack(),
    },
    [Channel.VIRTUAL_TERMINAL]: {
      variant: 'secondary',
      text: 'Go Back',
      onClick: () => (options.length > 1 ? navigateBack() : navigate('/')),
    },
  };

  const button = buttonConfig[qrChannel];

  const opts: QRCodeRenderersOptions = {
    width: 250,
    errorCorrectionLevel: 'H',
    margin: 2,
    color: {
      ...(theme?.colors || {}),
    },
  };

  useEffect(() => {
    if (canvasRef.current && payLink) {
      QRCode.toCanvas(
        canvasRef.current,
        payLink,
        opts,
        (error: Error | null | undefined) => {
          if (error) {
            logger.error('Error generating QR code:', error);
          }
        }
      );
    }
  }, [payLink]);

  return (
    <div id="tz-achPayLink-qr" className={theme?.container}>
      {qrChannel != Channel.VIRTUAL_TERMINAL && (
        <span className={styles.centerAlign}>
          <img src={loader} alt="Loading" className={theme?.loader} />
        </span>
      )}
      <span className={clsx(styles.centerAlign, styles.payStatement)}>
        Pay{' '}
        <span className={styles.amountText}>
          {`$${toDollars((payment?.originalAmount || 0) + (payment?.feeAmount || 0))}`}
        </span>
      </span>
      <div className={clsx(styles.centerAlign, styles.margin)}>
        <div className={clsx(styles.canvas, theme?.canvas)}>
          <canvas ref={canvasRef} className={styles.canvasRadius}></canvas>
          <p className={clsx(styles.centerAlign, styles.text)}>Scan to Pay</p>
        </div>
      </div>
      <span className={styles.centerAlign}>
        <Button
          variant={button.variant}
          text={button.text}
          disabled={false}
          onClick={() => {
            resetPayment();
            navigateBack();
            button.onClick();
          }}
          className={
            qrChannel === Channel.VIRTUAL_TERMINAL ? styles.goBackButton : ''
          }
        ></Button>
      </span>
    </div>
  );
};

export default QRCodeComponent;
