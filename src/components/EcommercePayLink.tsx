import { h } from 'preact';
import { useContext, useEffect } from 'preact/hooks';
import { useRoute } from 'wouter-preact';
import loader from '../assets/achLoading.gif';
import { toDollars } from '../utils/currencyUtils';
import styles from './EcommercePayLink.css';
import clsx from 'clsx';
import { GlobalsContext } from '../AppContext';
import useLogger from '../hooks/useLogger';

const EcommercePayLink = () => {
  const logger = useLogger();

  const [_match, params] = useRoute('/ecommerce/*');

  if (!params) {
    return <div>No Pay Link</div>;
  }

  const { payment, newTabRef } = useContext(GlobalsContext);

  const payLink = params['0'];

  useEffect(() => {
    // Open the partner's page in a new tab after a delay
    const timer = setTimeout(() => {
      newTabRef.current.tab = window.open(payLink, '_blank');
      newTabRef.current.url = payLink;
    }, 3000); // 3-second delay to allow the user to read the message

    return () => {
      // Clean up the timer on unmount and close the tab if it was opened
      clearTimeout(timer);
      if (newTabRef.current.tab && !newTabRef.current.tab.closed) {
        newTabRef.current.tab.close();
        newTabRef.current.url = null;
      }
    };
  }, [payLink]);

  return (
    <div id="tz-achPayLink-qr">
      <span className={styles.centerAlign}>
        <img src={loader} alt="Loading" />
      </span>
      <span className={clsx(styles.centerAlign, styles.payStatement)}>
        Pay{' '}
        <span className={styles.amountText}>
          {`$${toDollars((payment?.originalAmount || 0) + (payment?.feeAmount || 0))}`}
        </span>
      </span>
      <div className={clsx(styles.centerAlign, styles.margin)}>
          <p className={clsx(styles.centerAlign, styles.text)}>
            You are being redirected to complete your payment
          </p>
      </div>
    </div>
  );
};

export default EcommercePayLink;
