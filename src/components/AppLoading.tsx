import { h, FunctionComponent } from 'preact';
import { AppLoadingLayout } from '../layout/OverlayCustomComponents';
import Overlay from './Overlay';

const AppLoading: FunctionComponent = () => {
  const overlayContext = {
    customComponent: <AppLoadingLayout />,
    disableButtonsOnProgress: true,
  };
  return <Overlay context={overlayContext} message={undefined} />;
};

export default AppLoading;
