import { h } from 'preact';

export const ECommACHIcon = ({ className = '' }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    className={className}
  >
    <path
      d="M13.75 1.25H6.25C5.75272 1.25 5.27581 1.44754 4.92417 1.79917C4.57254 2.15081 4.375 2.62772 4.375 3.125V16.875C4.375 17.3723 4.57254 17.8492 4.92417 18.2008C5.27581 18.5525 5.75272 18.75 6.25 18.75H13.75C14.2473 18.75 14.7242 18.5525 15.0758 18.2008C15.4275 17.8492 15.625 17.3723 15.625 16.875V3.125C15.625 2.62772 15.4275 2.15081 15.0758 1.79917C14.7242 1.44754 14.2473 1.25 13.75 1.25ZM12.5 5H7.5C7.33424 5 7.17527 4.93415 7.05806 4.81694C6.94085 4.69973 6.875 4.54076 6.875 4.375C6.875 4.20924 6.94085 4.05027 7.05806 3.93306C7.17527 3.81585 7.33424 3.75 7.5 3.75H12.5C12.6658 3.75 12.8247 3.81585 12.9419 3.93306C13.0592 4.05027 13.125 4.20924 13.125 4.375C13.125 4.54076 13.0592 4.69973 12.9419 4.81694C12.8247 4.93415 12.6658 5 12.5 5Z"
      fill="#666E7A"
    />
  </svg>
);

export const ECommCardIcon = ({ className = '' }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    className={className}
  >
    <path
      d="M17.5 3.75H2.5C2.16848 3.75 1.85054 3.8817 1.61612 4.11612C1.3817 4.35054 1.25 4.66848 1.25 5V15C1.25 15.3315 1.3817 15.6495 1.61612 15.8839C1.85054 16.1183 2.16848 16.25 2.5 16.25H17.5C17.8315 16.25 18.1495 16.1183 18.3839 15.8839C18.6183 15.6495 18.75 15.3315 18.75 15V5C18.75 4.66848 18.6183 4.35054 18.3839 4.11612C18.1495 3.8817 17.8315 3.75 17.5 3.75ZM10.625 13.75H9.375C9.20924 13.75 9.05027 13.6842 8.93306 13.5669C8.81585 13.4497 8.75 13.2908 8.75 13.125C8.75 12.9592 8.81585 12.8003 8.93306 12.6831C9.05027 12.5658 9.20924 12.5 9.375 12.5H10.625C10.7908 12.5 10.9497 12.5658 11.0669 12.6831C11.1842 12.8003 11.25 12.9592 11.25 13.125C11.25 13.2908 11.1842 13.4497 11.0669 13.5669C10.9497 13.6842 10.7908 13.75 10.625 13.75ZM15.625 13.75H13.125C12.9592 13.75 12.8003 13.6842 12.6831 13.5669C12.5658 13.4497 12.5 13.2908 12.5 13.125C12.5 12.9592 12.5658 12.8003 12.6831 12.6831C12.8003 12.5658 12.9592 12.5 13.125 12.5H15.625C15.7908 12.5 15.9497 12.5658 16.0669 12.6831C16.1842 12.8003 16.25 12.9592 16.25 13.125C16.25 13.2908 16.1842 13.4497 16.0669 13.5669C15.9497 13.6842 15.7908 13.75 15.625 13.75ZM2.5 6.875V5H17.5V6.875H2.5Z"
      fill="#666E7A"
    />
  </svg>
);
