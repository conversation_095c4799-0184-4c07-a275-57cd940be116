import { h } from 'preact';

const Icon = ({ className = '' }) => {
  return (
    <svg
      width="53"
      height="52"
      viewBox="0 0 53 52"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M45.9985 26C45.9985 35.665 38.1635 43.5 28.4985 43.5C20.3369 43.5 13.4802 37.9128 11.5446 30.3545C11.3941 29.7669 10.8776 29.3346 10.2711 29.3346V29.3346C9.48838 29.3346 8.89101 30.0398 9.0782 30.7998C11.228 39.5273 19.1074 46 28.4985 46C39.5442 46 48.4985 37.0457 48.4985 26C48.4985 14.9543 39.5442 6 28.4985 6C19.1064 6 11.2263 12.474 9.07752 21.203C8.89045 21.9629 9.4878 22.668 10.2704 22.668V22.668C10.877 22.668 11.3936 22.2356 11.544 21.6479C13.4787 14.0884 20.336 8.5 28.4985 8.5C38.1635 8.5 45.9985 16.335 45.9985 26Z"
        fill="#404040"
      />
      <path
        d="M6.83594 12.668L9.33594 22.668L19.3359 19.3346"
        stroke="#404040"
        stroke-width="2.5"
      />
      <path
        d="M35.625 29.75C35.6235 31.0425 35.1094 32.2816 34.1955 33.1955C33.2816 34.1094 32.0425 34.6235 30.75 34.625H29.625V35.75C29.625 36.0484 29.5065 36.3345 29.2955 36.5455C29.0845 36.7565 28.7984 36.875 28.5 36.875C28.2016 36.875 27.9155 36.7565 27.7045 36.5455C27.4935 36.3345 27.375 36.0484 27.375 35.75V34.625H26.25C24.9575 34.6235 23.7184 34.1094 22.8045 33.1955C21.8906 32.2816 21.3765 31.0425 21.375 29.75C21.375 29.4516 21.4935 29.1655 21.7045 28.9545C21.9155 28.7435 22.2016 28.625 22.5 28.625C22.7984 28.625 23.0845 28.7435 23.2955 28.9545C23.5065 29.1655 23.625 29.4516 23.625 29.75C23.625 30.4462 23.9016 31.1139 24.3938 31.6062C24.8861 32.0984 25.5538 32.375 26.25 32.375H30.75C31.4462 32.375 32.1139 32.0984 32.6062 31.6062C33.0984 31.1139 33.375 30.4462 33.375 29.75C33.375 29.0538 33.0984 28.3861 32.6062 27.8938C32.1139 27.4016 31.4462 27.125 30.75 27.125H27C25.7071 27.125 24.4671 26.6114 23.5529 25.6971C22.6386 24.7829 22.125 23.5429 22.125 22.25C22.125 20.9571 22.6386 19.7171 23.5529 18.8029C24.4671 17.8886 25.7071 17.375 27 17.375H27.375V16.25C27.375 15.9516 27.4935 15.6655 27.7045 15.4545C27.9155 15.2435 28.2016 15.125 28.5 15.125C28.7984 15.125 29.0845 15.2435 29.2955 15.4545C29.5065 15.6655 29.625 15.9516 29.625 16.25V17.375H30C31.2925 17.3765 32.5316 17.8906 33.4455 18.8045C34.3594 19.7184 34.8735 20.9575 34.875 22.25C34.875 22.5484 34.7565 22.8345 34.5455 23.0455C34.3345 23.2565 34.0484 23.375 33.75 23.375C33.4516 23.375 33.1655 23.2565 32.9545 23.0455C32.7435 22.8345 32.625 22.5484 32.625 22.25C32.625 21.5538 32.3484 20.8861 31.8562 20.3938C31.3639 19.9016 30.6962 19.625 30 19.625H27C26.3038 19.625 25.6361 19.9016 25.1438 20.3938C24.6516 20.8861 24.375 21.5538 24.375 22.25C24.375 22.9462 24.6516 23.6139 25.1438 24.1062C25.6361 24.5984 26.3038 24.875 27 24.875H30.75C32.0425 24.8765 33.2816 25.3906 34.1955 26.3045C35.1094 27.2184 35.6235 28.4575 35.625 29.75Z"
        fill="#404040"
      />
    </svg>
  );
};

export default Icon;
