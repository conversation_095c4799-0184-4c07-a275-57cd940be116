import { h, FunctionalComponent, createContext } from 'preact';
import { useContext, useEffect, useMemo } from 'preact/hooks';
import {
  ConfigContext,
  GlobalsContext,
  ServiceContext,
} from '../../AppContext';
import useLogger from '../../hooks/useLogger';
import { useStatusPolling } from '../../hooks/usePaymentStatusPolling';
import { DeviceContext } from '../../context/DeviceProvider';
import { usePaymentActionButtons } from '../../hooks/usePaymentActionButtons';
import { Channel, WidgetApi } from '../../models';
import { handlePaymentStatus } from '../../utils/paymentStatusHandler';

interface PaymentStatusPollingContextType {
  stopPolling: () => void;
  startPolling: (paymentId: string, type: 'payment' | 'invoice') => void;
  isPolling: boolean;
}

export const PaymentStatusPollingContext =
  createContext<PaymentStatusPollingContextType | null>(null);

interface PaymentStatusPollingProviderProps {
  children: preact.ComponentChildren;
}

const PaymentStatusPollingProvider: FunctionalComponent<
  PaymentStatusPollingProviderProps
> = ({ children }) => {
  const logger = useLogger();
  const { setOverlayContext, setOverlayVisible, payment, newTabRef } =
    useContext(GlobalsContext);
  const { currentPath } = useContext(GlobalsContext);
  const config = useContext(ConfigContext);
  const service = useContext(ServiceContext);
  const deviceContext = useContext(DeviceContext);

  if (!deviceContext) {
    logger.warn('No device context found');
    return null;
  }

  const { getPaymentMethodByProcessorId, getProcessorNameByProcessorId } =
    deviceContext;

  const getProcessorDetails = useMemo(
    () => (processorId: string | null) => {
      const processorName = processorId
        ? (getProcessorNameByProcessorId(processorId) ?? 'Unknown Processor')
        : 'Unknown Processor';
      const paymentMethod = processorId
        ? (getPaymentMethodByProcessorId(processorId) ?? 'Unknown Method')
        : 'Unknown Method';

      return { processorName, paymentMethod };
    },
    [getPaymentMethodByProcessorId, getProcessorNameByProcessorId]
  );

  const { isPolling, startPolling, stopPolling } = useStatusPolling({
    service: service as WidgetApi,
    onSuccess: statusData => {
      if (!payment) return;

      const { processorName, paymentMethod } = getProcessorDetails(
        statusData.processorId
      );

      handlePaymentStatus({
        status: 'approved',
        data: statusData,
        processorName,
        paymentMethod,
        payment,
        setOverlayVisible,
        setOverlayContext,
        primaryActionButton,
        changePaymentMethodButton,
        channel: config.channel as Channel,
        newTabRef
      });
    },
    onError: statusData => {
      if (!payment) return;

      const { processorName, paymentMethod } = getProcessorDetails(
        statusData.processorId
      );

      handlePaymentStatus({
        status: 'error',
        data: statusData,
        processorName,
        paymentMethod,
        payment,
        setOverlayVisible,
        setOverlayContext,
        primaryActionButton,
        changePaymentMethodButton,
        channel: config.channel as Channel,
      });
    },
    onTimeout: (id: string) => {
      logger.error(`Payment status polling timeout, paymentId: ${id}`);
    },
  });

  const { primaryActionButton, changePaymentMethodButton } =
    usePaymentActionButtons({});

  const isPaymentProcessingPath = useMemo(
    () =>
      ['/qr', '/paymentProcessing'].some(path => currentPath.startsWith(path)),
    [currentPath]
  );

  const shouldStopForFFD_VT = useMemo(
    () =>
      (config.channel === Channel.FFD ||
        config.channel === Channel.VIRTUAL_TERMINAL) &&
      (payment?.asyncUpdateReceived ||
        payment?.cancel ||
        (!payment?.paymentId && !payment?.invoiceId) ||
        !isPaymentProcessingPath),
    [
      config.channel,
      payment?.asyncUpdateReceived,
      payment?.cancel,
      isPaymentProcessingPath,
    ]
  );

  const shouldStopForEcomm = useMemo(
    () =>
      config.channel === Channel.ECOMMERCE &&
      (payment?.asyncUpdateReceived ||
        (!payment?.paymentId && !payment?.invoiceId)),
    [config.channel, payment?.asyncUpdateReceived]
  );

  // Handles stopping decisions
  useEffect(() => {
    if (!payment || !isPolling) return;

    logger.debug('Polling Effect 1 Triggered:', {
      paymentId: payment?.paymentId,
      invoiceId: payment?.invoiceId,
      currentPath,
      isPolling,
    });

    if (shouldStopForFFD_VT || shouldStopForEcomm) {
      stopPolling();
      return;
    }

    return () => {};
  }, [
    payment?.asyncUpdateReceived,
    payment?.cancel,
    currentPath,
    shouldStopForFFD_VT,
    shouldStopForEcomm,
    isPolling,
    startPolling,
    stopPolling,
  ]);

  return (
    <PaymentStatusPollingContext.Provider value={{ stopPolling, startPolling, isPolling }}>
      {children}
    </PaymentStatusPollingContext.Provider>
  );
};

export const usePaymentStatusPolling = () => {
  const context = useContext(PaymentStatusPollingContext);
  if (!context) {
    throw new Error(
      'usePaymentStatusPolling must be used within a PaymentStatusPollingProvider'
    );
  }
  return context;
};

export default PaymentStatusPollingProvider;
