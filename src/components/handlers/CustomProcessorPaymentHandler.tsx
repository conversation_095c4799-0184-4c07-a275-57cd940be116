import { h, FunctionalComponent } from 'preact';
import { useContext, useEffect } from 'preact/hooks';
import { GlobalsContext } from '../../AppContext';
import { DeviceContext } from '../../context/DeviceProvider';
import { usePaymentDevice } from '../../hooks/usePaymentDevice';
import useLogger from '../../hooks/useLogger';

const CustomProcessorPaymentHandler: FunctionalComponent = () => {
  const logger = useLogger();

  const deviceContext = useContext(DeviceContext);
  const { payment, navigate, paymentProcessor, setPaymentProcessor } =
    useContext(GlobalsContext);

  if (!payment) {
    return null;
  }
  const paymentDevice = usePaymentDevice(
    payment,
    deviceContext?.paymentMethodByDeviceLocation
  );

  useEffect(() => {
    if (paymentDevice && !paymentProcessor) {
      //  || !paymentProcessor?.paymentSent
      logger.debug('Set Custom Processor');
      setPaymentProcessor({
        paymentMethod: 'ATM', // todo: find method
        locationId: paymentDevice.posLocationId || paymentDevice.locationId,
        processorId: paymentDevice.processorId,
        processorName: paymentDevice.processorName,
        paymentDeviceConfigurationId: paymentDevice.id,
        paymentSent: false,
      });
      navigate('/paymentProcessing/prepaymentOption');
    }
  }, [paymentDevice, paymentProcessor, setPaymentProcessor, navigate]);

  return null;
};

export default CustomProcessorPaymentHandler;
