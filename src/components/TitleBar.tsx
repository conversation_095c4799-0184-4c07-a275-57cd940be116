import { h } from 'preact';
import style from './titlebar.css';
import { useContext } from 'preact/hooks';
import { GlobalsContext } from '../AppContext';
import { useTheme } from '../context/ThemeProvider';

const TitleBar = () => {
  const { widgetOpen, payment } = useContext(GlobalsContext);
  const { theme } = useTheme();

  return (
    <div
      className={style.root}
      style={{ cursor: theme?.positionFixed ? 'pointer' : 'default' }}
    >
      <h4 className={style.title}>
        {widgetOpen && payment?.customer?.firstName ? (
          `Welcome back, ${payment?.customer?.firstName}`
        ) : (
          <img src="https://app.dev.treezpay.com/static/media/treez-nav.6c41cc95b1124ff1b3b060422aad462a.svg"></img>
        )}
      </h4>
    </div>
  );
};

export default TitleBar;
