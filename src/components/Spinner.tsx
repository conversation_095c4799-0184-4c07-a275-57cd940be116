import { h } from 'preact';
import { useState, useEffect } from 'preact/hooks';

interface SpinnerProps {
  isLoading: boolean;
}

const Spinner: preact.FunctionalComponent<SpinnerProps> = ({ isLoading }) => {
  if (!isLoading) {
    return null; // Don't render anything if isLoading is false
  }
  const [loadingText, setLoadingText] = useState('');
  const loadingLetters = 'LOADING';
  const typingSpeed = 200; // Adjust the typing speed as needed
  const repeatDelay = 0; // Adjust the delay before repeating as needed

  useEffect(() => {
    let index = 0;

    const interval = setInterval(() => {
      if (index >= loadingLetters.length) {
        setTimeout(() => {
          setLoadingText('');
          index = 0; // Reset index
        }, repeatDelay);
      } else {
        setLoadingText(prevText => prevText + loadingLetters[index]);
        index++;
      }
    }, typingSpeed);

    return () => clearInterval(interval);
  }, []);
  return (
    <div>
      <h2>{loadingText}</h2>
    </div>
  );
};

export default Spinner;
