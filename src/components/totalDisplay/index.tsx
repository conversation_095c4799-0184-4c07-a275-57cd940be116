import { Fragment, h } from 'preact';
import { useCallback, useContext, useEffect, useMemo } from 'preact/hooks';
import { toDollars } from '../../utils/currencyUtils';
import { GlobalsContext } from '../../AppContext';
import styles from './totalDisplay.css';
import dropdownArrowIcon from '../../assets/caretCircleGreen.svg';
import roundingIcon from '../../assets/clockwiseArrowYellow.svg';
import CardIcon from '../../components/icons/card';
import clsx from 'clsx';
import { PreviewContext } from '../../context/PreviewProvider';
import { PAYMENT_METHOD_LABELS } from '../../constants/appConstants';

const TotalDisplay = () => {
  const {
    payment,
    setOverlayVisible,
    setOverlayContext,
    currentPath,
    paymentProcessor,
    showTotalDetails,
    setShowTotalDetails,
  } = useContext(GlobalsContext);
  const previewContext = useContext(PreviewContext);
  const isTipsView = currentPath === '/tips';
  const isATMPaymentMethodSelected = paymentProcessor?.paymentMethod === 'ATM';

  const totalAmount = useMemo(() => {
    if (previewContext?.previewTotals?.totalAmount) {
      return Number(previewContext.previewTotals.totalAmount);
    }
    return payment?.originalAmount || 0;
  }, [previewContext, payment]);

  const feeAmount = useMemo(() => {
    if (previewContext) {
      if (previewContext?.previewTotals?.feeAmount) {
        return Number(previewContext.previewTotals.feeAmount);
      }
    }
    return 0;
  }, [previewContext, payment]);

  const tipAmount = useMemo(() => {
    if (previewContext) {
      if (previewContext?.previewTotals?.tipAmount) {
        return Number(previewContext.previewTotals.tipAmount);
      }
    }
    return payment?.tip || 0;
  }, [previewContext, payment]);

  const cashbackAmount = useMemo(() => {
    if (previewContext) {
      if (previewContext?.previewTotals?.cashbackAmount) {
        return Number(previewContext.previewTotals.cashbackAmount);
      }
    }
    return 0;
  }, [previewContext, payment]);

  const roundAmount = useMemo(() => {
    if (previewContext) {
      if (previewContext?.previewTotals?.roundAmount) {
        return Number(previewContext.previewTotals.roundAmount);
      }
    }
    return 0;
  }, [previewContext, payment]);

  const toggleExpand = useCallback(() => {
    setShowTotalDetails(prev => !prev);
    setOverlayContext({ darkBackground: true });
    setOverlayVisible(prev => !prev);
  }, [setOverlayContext, setOverlayVisible, setShowTotalDetails]);

  useEffect(() => {
    let timer: number;

    if (showTotalDetails) {
      timer = window.setTimeout(() => {
        setShowTotalDetails(false);
        setOverlayVisible(false);
      }, 20000);
    }

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [showTotalDetails, setOverlayVisible, setShowTotalDetails]);

  const RoundingInfoItem = () => {
    return isATMPaymentMethodSelected && isTipsView ? (
      <div
        className={clsx(styles.roundingLineitem, styles.marginY4)}
      >
        <img src={roundingIcon} className={styles.roundingIcon} />
        Rounded to nearest{' '}
        <span className={styles.roundingValue}>${toDollars(roundAmount)}</span>
      </div>
    ) : null;
  };

  return (
    <div
      className={clsx(
        styles.container,
        showTotalDetails && styles.containerBorder
      )}
      onClick={toggleExpand}
      id="tz-total-display-drawer"
    >
      <div className={clsx(
        styles.header,
        isTipsView && styles.alignStart
      )}>
        <div>
          <div role="button" className={styles.headerTotalAmt}>
            Estimated Total:
            <span className={styles.totalAmount}>{`$${toDollars(totalAmount)}`}</span>
          </div>
          {!showTotalDetails && (roundAmount || 0) > 0 && <RoundingInfoItem />}
        </div>
        <img
          src={dropdownArrowIcon}
          className={clsx(
            styles.caretIcon,
            showTotalDetails ? styles.caretIconExpanded : styles.caretIconCollapsed
          )}
        />
      </div>
      <div
        className={clsx(styles.breakdown, showTotalDetails && styles.expanded)}
        onClick={(e: Event) => e.stopPropagation()}
      >
        <hr className={styles.breakdownHr} />
        <div className={styles.breakdownContent}>
          <div className={styles.breakdownLineItem}>
            <span>Original Price</span>
            <span>{`$${toDollars(payment?.originalAmount)}`}</span>
          </div>
          {(payment?.discount ?? 0) > 0 && (
            <div
              className={clsx(styles.breakdownLineItem, styles.dottedBorder)}
            >
              <span>Total Discounts</span>
              <span>{`-$${toDollars(payment?.discount)}`}</span>
            </div>
          )}
          {(payment?.taxes ?? 0) > 0 && (
            <div
              className={clsx(styles.breakdownLineItem, styles.marginY4)}
            >
              <span>Total Tax</span>
              <span>{`$${toDollars(payment?.taxes)}`}</span>
            </div>
          )}
          {(tipAmount || 0) > 0 && (
            <div
              className={clsx(styles.breakdownLineItem, styles.dottedBorder, styles.marginTop4)}
            >
              <span>Tip</span>
              <span>{`$${toDollars(tipAmount)}`}</span>
            </div>
          )}
          {isTipsView && (
            <Fragment>
              {(feeAmount || 0) > 0 && (
                <div className={clsx(
                  styles.breakdownLineItem,
                  styles.dottedBorder,
                  styles.paddingTop8
                )}>
                  <span>Convenience fee</span>
                  <span>{`$${toDollars(feeAmount)}`}</span>
                </div>
              )}
              {isATMPaymentMethodSelected && (
                <div
                  className={clsx(
                    styles.breakdownLineItem,
                    styles.dottedBorder,
                    styles.marginY4,
                  )}
                >
                  <span>Expected change</span>
                  <span className={styles.expectedChange}>
                    ({`$${toDollars(cashbackAmount)}`})
                  </span>
                </div>
              )}
              {(roundAmount || 0) > 0 && <RoundingInfoItem />}
              <div className={styles.payingViaContainer}>
                <div className={styles.line}></div>
                <div className={styles.payingVia}>
                  Paying via <CardIcon className={styles.payingViaIcon} />{' '}
                  <span className={styles.payingViaLabel}>
                    {paymentProcessor?.paymentMethod
                      ? PAYMENT_METHOD_LABELS?.[paymentProcessor?.paymentMethod]
                      : ''}
                  </span>
                </div>
                <div className={styles.line}></div>
              </div>
            </Fragment>
          )}
        </div>
      </div>
    </div>
  );
};

export default TotalDisplay;
