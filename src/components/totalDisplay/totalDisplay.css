.container {
  z-index: 1001;
  padding: 20px var(--tz-padding-x);
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  box-shadow:
    0px 1px 2px -1px rgba(0, 0, 0, 0.1),
    0px 1px 3px 0px rgba(0, 0, 0, 0.1);
  border-radius: 0px 0px 24px 24px;
  position: absolute;
  top: var(--tz-title-height);
  left: 0;
  right: 0;
  cursor: pointer;
  height: auto;
}

.header {
  height: calc(var(--tz-top-drawer-height) - 40px);
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.headerTotalAmt {
  color: #4d5664;
  font-size: 24px;
  font-weight: 500;
  line-height: 28.8px;
}

.totalAmount {
  color: #404040;
  font-size: 28px;
  font-weight: 700;
  line-height: 33.6px;
  padding-left: 4px;
}

.caretIcon {
  width: 72px;
  height: 72px;
  transition: transform 0.4s ease-in-out;
  display: block;
}

.caretIconExpanded {
  transform: rotate(180deg);
}

.caretIconCollapsed {
  transform: rotate(0deg);
}

.breakdown {
  background-color: #fff;
  overflow: hidden;
  transition: max-height 0.4s ease-in-out;
  max-height: 0px;
  color: var(--tz-text-color-secondary);
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 100%;
}

.breakdown.expanded {
  max-height: 400px;
}

.breakdownHr {
  border: none;
  border-top: 1px solid var(--tz-color-border);
  width: 100%;
  margin: 0px;
}

.breakdownContent {
  width: 100%;
  padding: 16px 0 0 0;
}

.breakdownLineItem {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
}

.roundingLineitem {
  color: #985406;
  font-size: 16px;
  font-style: italic;
  font-weight: 400;
  line-height: 24px;
  display: inline-flex;
  margin-bottom: 8px;
}

.roundingIcon {
  margin-right: 8px;
}

.roundingValue {
  color: #985406;
  font-weight: 600;
  margin-left: 4px;
}

.payingViaContainer {
  margin-top: 4px;
  display: flex;
  align-items: center;
}

.line {
  flex-grow: 1;
  height: 1px;
  background: #bfbfbf;
}

.payingVia {
  height: 46px;
  display: flex;
  padding: 8px 24px;
  justify-content: center;
  align-items: center;
  width: 307px;
  border-radius: 24px;
  border: 1px solid #bfbfbf;
  color: var(--tz-text-color-secondary);
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
}

.payingViaLabel {
  font-weight: 500;
}

.payingViaIcon {
  width: 28px;
  height: 28px;
  margin: 0 6px 0 8px;
}

.dottedBorder {
  border-top: 1px dashed var(--tz-color-border);
}

.expectedChange {
  color: #3e7a18;
  font-weight: 500;
}

.alignStart {
  align-items: flex-start;
}

.marginBottom0 {
  margin-bottom: 0;
}

.marginTop4 {
  margin-top: 4px;
}

.marginTop8 {
  margin-top: 8px;
}

.paddingTop8 {
  padding-top: 8px;
}

.containerBorder {
  border-radius: 0;
}

.marginY4 {
  margin: 4px 0;
}
