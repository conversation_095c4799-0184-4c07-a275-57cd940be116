.payStatement {
  font-size: 32px;
  font-weight: 400;
  color: var(--tz-text-color-primary);
}

.centerAlign {
  display: flex;
  text-align: center;
  justify-content: center;
}

.subtext {
  font-size: 24px;
  margin-bottom: 8px;
  margin-top: 8px;
}

.margin {
  margin-top: 20px;
  margin-bottom: 32px;
}

.canvas {
  color: #1a4007;
  background-color: #1a4007;
  padding-top: 20px;
  padding-right: 20px;
  padding-left: 20px;
  border-radius: 40px;
  text-align: center;
  justify-content: center;
  box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.1);
}

p.text {
  color: var(--tz-text-color-secondary);
  font-size: 24px;
  font-weight: 500;
  margin: 1rem;
}

.canvasRadius {
  border-radius: 24px;
}

.amountText {
  font-weight: 600;
  padding-left: 10px;
}
