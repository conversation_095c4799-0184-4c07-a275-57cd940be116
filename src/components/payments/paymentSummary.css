.table {
  width: 100%;
  margin: 24px 0 48px 0;
}

.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0px;
  color: var(--tz-text-color-secondary);
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
}

.toPayLabel {
  color: #4D5664;
  font-size: 24px;
  font-weight: 500;
  line-height: 28.8px
}

.toPayAmount {
  color: #333D4E;
  font-size: 28px;
  font-weight: 600;
  line-height: 33.6px;
}

.dottedLine {
  border-bottom: 1px dashed #CCCFD3;
  margin: 4px 0;
  width: 100%;
}

.bold {
  font-weight: bold;
}

.totalRow {
  font-size: 1.5em;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.totalAmount {
  text-align: right;
}

.roundingInfo {
  color: #985406;
  font-size: 16px;
  font-style: italic;
  font-weight: 400;
  line-height: 24px;
  display: inline-flex;
}

.roundingInfo strong {
  font-weight: 600;
}

.roundingText {
  margin-left: 8px;
}

span.expectedChangeValue {
  color: #3E7A18;
  font-weight: 600;
}

.payingVia {
  text-align: center;
  color: var(--tz-text-color-secondary);
  font-size: 20px;
  font-weight: 400;
  line-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
}

.payingViaLabel {
  font-weight: 500;
}

.payingViaIcon {
  width: 32px;
  height: 32px;
  margin: 0 6px 0 8px;
}

.ctaContainer {
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  height: var(--tz-footer-height);
  padding: 24px var(--tz-padding-x);
  border-top: 1px solid #CCCFD3;
  background: #FFF;
}

.ctaFixed {
  position: fixed;
}

.ctaAbsolute {
  position: absolute;
}

.containerPaddingBtm {
  padding-bottom: var(--tz-footer-height);
}
