import { h } from 'preact';
import { JSX } from 'preact/jsx-runtime';
import styles from './customPaymentHeader.css';

interface PaymentHeaderProps {
  headingText: string;
  paragraphText: string | JSX.Element;
}

const CustomPaymentHeader = ({ headingText, paragraphText }: PaymentHeaderProps) => {
  return (
    <div>
      <div className={styles.heading}>{headingText}</div>
      {typeof paragraphText === 'string' ? (
        <p className={styles.subtext}>{paragraphText}</p>
      ) : (
        paragraphText
      )}
    </div>
  );
};

export default CustomPaymentHeader;
