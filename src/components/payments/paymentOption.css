.layout {
  height: 100%;
}

.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32px;
  justify-content: center;
  overflow-y: auto;
  margin-top: 40px;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  height: 220px;
  cursor: pointer;
  text-align: center;
  border: 1px solid #F5F5F5;
  box-shadow: 0px 1px 2px -1px rgba(0, 0, 0, 0.10), 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
}

.icon {
  width: 52px;
  height: 52px;
}

.label {
  color: #262626;
  font-size: 28px;
  font-weight: 500;
  line-height: 32px;
  margin-top: 8px;
}

.roundingInfo {
  color: var(--tz-text-color-secondary);
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

.feeInfo {
  color: var(--tz-text-color-secondary);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
