import { h } from 'preact';
import PaymentOptionsGrid from './PaymentOptionsGrid';
import RoundedDebitIcon from '../../components/icons/roundedDebit';
import debitIcon from '../../assets/debit.svg';
import creditIcon from '../../assets/credit.svg';
import achIcon from '../../assets/ach.svg';
import othersIcon from '../../assets/other.svg';
import { useContext } from 'preact/hooks';
import CustomPaymentHeader from './CustomPaymentHeader';
import { DeviceContext } from '../../context/DeviceProvider';
import { GlobalsContext } from '../../AppContext';
import { generatePaymentOptionSelectedEvent } from '../../events/PaymentEvents';
import { useEventDispatcher } from '../../context/EventDispatcherProvider';
import useLogger from '../../hooks/useLogger';
import styles from './paymentOption.css';

type PaymentOption = {
  bgColor?: string;
  isActive?: boolean;
  label: string;
  fee: string;
  icon: string | h.JSX.Element;
  link?: string; // Optional field if some options don't have a link
  onClick: () => void;
};

type PaymentOptionsContainerProps = {
  paymentOptions?: PaymentOption[]; // Marking paymentOptions as optional
  id?: string;
};

const PaymentOptionsContainer = ({
  paymentOptions,
  id,
}: PaymentOptionsContainerProps) => {
  const logger = useLogger();

  const { navigate, setOverlayContext } = useContext(GlobalsContext);
  const { dispatchEvent } = useEventDispatcher();

  const deviceContext = useContext(DeviceContext);
  if (!deviceContext) {
    return <div>Loading...</div>;
  }

  const {
    paymentMethods,
    loading,
    getPaymentMethodFee,
    getPaymentMethodRoundingAmount,
    getProcessorNameFeeMapping,
  } = deviceContext;

  const getAchFee = (): string | number | undefined => {
    const achProcessors = paymentMethods['ACH'] || [];
    const achProcessorCount = achProcessors.length;

    if (achProcessorCount === 1) {
      return getPaymentMethodFee('ACH'); // Return fee for single processor
    }

    if (achProcessorCount > 1) {
      const achProcessorFeeMapping = getProcessorNameFeeMapping('ACH');
      const processorsWithFees = Object.keys(achProcessorFeeMapping).filter(
        processorName => {
          const feeAmount = Number(achProcessorFeeMapping[processorName]);
          return feeAmount > 0;
        }
      );

      if (processorsWithFees.length > 0) {
        return -1; // At least one processor has a fee
      } else {
        return 0; // No processors have fees
      }
    }

    // Default return for no ACH processors or unexpected cases
    return undefined;
  };

  const paymentMethodOptions: Record<string, any> = {
    Debit: {
      order: 1,
      label: 'Debit',
      fee: getPaymentMethodFee('Debit'),
      bgColor: loading ? null : '#FAF2FE',
      icon: debitIcon,
      onClick: () => {
        setOverlayContext(null);
        logger.debug('Debit Selected');
        if (loading) {
          return;
        }
        dispatchEvent(
          'paymentOptionSelected',
          generatePaymentOptionSelectedEvent('Debit')
        );
        navigate('/debit');
      },
    },
    ATM: {
      order: 0,
      label: 'Rounded Debit',
      fee: getPaymentMethodFee('ATM'),
      bgColor: loading ? null : '#FDF8E5',
      icon: <RoundedDebitIcon className={styles.icon} />,
      rounding: getPaymentMethodRoundingAmount('ATM'),
      onClick: () => {
        setOverlayContext(null);
        logger.debug('ATM Selected');
        if (loading) {
          return;
        }
        dispatchEvent(
          'paymentOptionSelected',
          generatePaymentOptionSelectedEvent('Rounded Debit')
        );
        navigate('/atm');
      },
    },
    ACH: {
      order: 3,
      label: 'ACH',
      fee: getAchFee(),
      icon: achIcon,
      bgColor: loading ? null : '#F0F8FF',
      onClick: () => {
        setOverlayContext(null);
        logger.debug('ACH Selected');
        if (loading) {
          return;
        }
        dispatchEvent(
          'paymentOptionSelected',
          generatePaymentOptionSelectedEvent('ACH')
        );
        navigate('/ach');
      },
    },
    Credit: {
      order: 4,
      label: 'Credit',
      fee: 0,
      icon: creditIcon,
      bgColor: '#FFEDED',
      onClick: () => {
        setOverlayContext(null);
        logger.debug('Credit Selected');
        dispatchEvent(
          'paymentOptionSelected',
          generatePaymentOptionSelectedEvent('Credit')
        );
        navigate('/credit');
      },
    },
    Others: {
      order: 5,
      label: 'Cash',
      fee: '',
      icon: othersIcon,
      bgColor: '#F0F0F0',
      onClick: () => {
        setOverlayContext(null);
        logger.debug('Others Selected');
        dispatchEvent(
          'paymentOptionSelected',
          generatePaymentOptionSelectedEvent('Other')
        );
      },
    },
  };

  const options = Object.keys(paymentMethods)
    .map((paymentMethod: string) => paymentMethodOptions[paymentMethod])
    .sort((a, b) => a.order - b.order);

  // todo: make configurable?
  options.push(paymentMethodOptions.Others);

  return (
    <div className={styles.layout} id={id}>
      <CustomPaymentHeader
        headingText="How would you like to pay?"
        paragraphText="Please ask the budtender for assistance."
      />
      <PaymentOptionsGrid options={paymentOptions || options} />
    </div>
  );
};

export default PaymentOptionsContainer;
