import { Fragment, FunctionalComponent, h } from 'preact';
import { toDollars } from '../../utils/currencyUtils';
import { useContext, useMemo } from 'preact/hooks';
import { PreviewContext } from '../../context/PreviewProvider';
import RightArrow from '../../assets/ArrowRight.svg';
import Button from '../buttons/button';
import styles from './paymentSummary.css';
import CustomPaymentHeader from './CustomPaymentHeader';
import { GlobalsContext } from '../../AppContext';
import CardIcon from '../../components/icons/card';
import roundingIcon from '../../assets/clockwiseArrowYellow.svg';
import clsx from 'clsx';
import { PAYMENT_METHOD_LABELS } from '../../constants/appConstants';
import { useTheme } from '../../context/ThemeProvider';

interface Payment {
  originalAmount?: number;
  discount?: number;
  rewardDollars?: number;
  taxes?: number;
  convenienceFee?: number;
  expectedChange?: number;
  cashbackAmount?: number
  rounding?: number;
  tip?: number;
}

interface TotalDisplayProps {
  payment?: Payment;
  isCheckoutDisabled?: boolean;
  handleSubmit: () => void;
  handleBack: () => void;
}

const PaymentSummary: FunctionalComponent<TotalDisplayProps> = ({
  payment,
  isCheckoutDisabled,
  handleSubmit,
  handleBack,
}) => {
  const previewContext = useContext(PreviewContext);
  const { paymentProcessor } = useContext(GlobalsContext);
  const isATMPaymentMethodSelected = paymentProcessor?.paymentMethod === 'ATM';
  const { theme } = useTheme();

  const {
    discount = 0,
    rewardDollars = 0,
    taxes = 0,
    tip = 0,
  } = payment || {};

  const originalAmount = useMemo(() => {
    if (previewContext?.previewTotals?.totalAmount) {
      return Number(previewContext.previewTotals.totalAmount);
    }
    return payment?.originalAmount || 0;
  }, [previewContext, payment]);

  const convenienceFee = useMemo(() => {
    if (previewContext?.previewTotals?.feeAmount) {
      return Number(previewContext.previewTotals.feeAmount);
    }
    return 0;
  }, [previewContext, payment]);

  const cashbackAmount = useMemo(() => {
    if (previewContext) {
      if (previewContext?.previewTotals?.cashbackAmount) {
        return Number(previewContext.previewTotals.cashbackAmount);
      }
    }
    return 0;
  }, [previewContext, payment]);


  const roundAmount = useMemo(() => {
    if (previewContext?.previewTotals?.roundAmount) {
      return Number(previewContext.previewTotals.roundAmount);
    }
    return 0;
  }, [previewContext]);

  const total = originalAmount;

  const PayingVia = () => (
    <div className={styles.payingVia}>
      Paying via <CardIcon className={styles.payingViaIcon} />
      <strong className={styles.payingViaLabel}>
        {paymentProcessor?.paymentMethod
          ? PAYMENT_METHOD_LABELS?.[paymentProcessor?.paymentMethod]
          : ''}
      </strong>
    </div>
  );

  return (
    <div className={!theme.positionFixed && styles.containerPaddingBtm} id="tz-payment-summary">
      <CustomPaymentHeader headingText='Payment Summary' paragraphText={<PayingVia />} />
      <div className={styles.table}>
        <div className={styles.row}>
          <span>Original Price</span>
          <span>${toDollars(payment?.originalAmount || 0)}</span>
        </div>
        {(discount ?? 0) > 0 && (
          <div className={styles.row}>
            <span>Total Discounts</span>
            <span>-${toDollars(discount)}</span>
          </div>
        )}
        {(taxes ?? 0) > 0 && (
          <Fragment>
            <div className={styles.dottedLine}></div>
            <div className={styles.row}>
              <span>Total Tax</span>
              <span>${toDollars(taxes)}</span>
            </div>
          </Fragment>
        )}
        <div className={styles.dottedLine}></div>
        <div className={styles.row}>
          <span>Convenience Fee</span>
          <span>${toDollars(convenienceFee)}</span>
        </div>
        <div className={styles.dottedLine}></div>
        {isATMPaymentMethodSelected && (
          <Fragment>
            <div className={styles.row}>
              <span>Expected Change</span>
              <span className={styles.expectedChangeValue}>${toDollars(cashbackAmount)}</span>
            </div>
            <div className={styles.roundingInfo}>
              <img src={roundingIcon} />
              <span className={styles.roundingText}>
                Rounding to nearest <strong>${toDollars(roundAmount)}</strong>
              </span>
            </div>
            <div className={styles.dottedLine}></div>
          </Fragment>
        )}

        <div className={styles.row}>
          <span className={styles.toPayLabel}>To Pay</span>
          <span className={styles.toPayAmount}>${toDollars(total)}</span>
        </div>
      </div>

      <div className={clsx(styles.ctaContainer, !theme.positionFixed ? styles.ctaFixed: styles.ctaAbsolute)}>
        <Button
          disabled={false}
          onClick={handleBack}
          text="Back"
          variant="secondary"
        />
        <Button
          disabled={isCheckoutDisabled || false}
          onClick={handleSubmit}
          text="Checkout"
          variant="primary"
          icon={RightArrow}
        />
      </div>
    </div>
  );
};

export default PaymentSummary;
