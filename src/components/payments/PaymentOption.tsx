import { Fragment, h } from 'preact';
import styles from './paymentOption.css';
import { toDollars } from '../../utils/currencyUtils';
import { formatFee } from '../../utils/stringUtils';

interface IProps {
  bgColor: string;
  icon: string | h.JSX.Element;
  label: string;
  fee?: number;
  processorCount?: number;
  rounding?: number;
  onClick: () => void;
  iconStyles?: Record<string, string>;
  id?: string;
}

const PaymentOption = ({
  bgColor,
  icon,
  label,
  fee,
  onClick,
  rounding,
  iconStyles = {},
  id,
}: IProps) => {
  const formattedFee = formatFee(fee);
  const optionId =
    id || `tz-${label.replace(/\s+/g, '-').toLowerCase()}-option`;

  return (
    <Fragment>
      <div
        className={styles.container}
        onClick={onClick}
        style={{
          backgroundColor: bgColor || '#f7f7f7',
        }}
        id={optionId}
      >
        {typeof icon === 'string' ? (
          <img
            className={styles.icon}
            src={icon}
            alt={label}
            style={iconStyles}
          />
        ) : (
          icon
        )}
        <div className={styles.label}>{label}</div>
        {rounding && rounding > 0 && (
          <div className={styles.roundingInfo}>
            (rounds to nearest ${toDollars(rounding)})
          </div>
        )}
        {formattedFee && <div className={styles.feeInfo}>{formattedFee}</div>}
      </div>
    </Fragment>
  );
};

export default PaymentOption;
