import { h } from 'preact';
import PaymentOption from './PaymentOption';
import styles from './paymentOption.css';

const PaymentOptionsGrid = ({ options, paymentMethod }: { options: any[]; paymentMethod?: string }) => {
    return (
        <div className={styles.grid}>
            {options.map((option: any) => (
                <PaymentOption
                   {...option}
                   paymentMethod={paymentMethod}
                />
            ))}
        </div>
    );
};

export default PaymentOptionsGrid;
