import { h, FunctionComponent } from 'preact';
import { ApiErrorLayout } from '../layout/OverlayCustomComponents';
import Overlay from './Overlay';
import { useContext } from 'preact/hooks';
import { GlobalsContext } from '../AppContext';
import Button from './buttons/button';
import useResetPayment from '../hooks/useResetPayment';

const ApiError: FunctionComponent = () => {
  const {
    apiError,
    setOverlayContext,
    setOverlayVisible,
    navigate,
    setApiError,
  } = useContext(GlobalsContext);

  if (!apiError) {
    return null;
  }

  const resetPayment = useResetPayment();

  const changePaymentMethodButton = (
    <Button
      variant="secondary"
      text="Change payment method"
      disabled={false}
      onClick={() => {
        setOverlayVisible(false);
        setOverlayContext(null);
        setApiError(null);
        resetPayment();
        navigate('/');
      }}
    ></Button>
  );

  const overlayContext = {
    customComponent: (
      <ApiErrorLayout
        error={apiError}
        secondaryActionButton={changePaymentMethodButton}
      />
    ),
    disableButtonsOnProgress: true,
  };
  return <Overlay context={overlayContext} />;
};

export default ApiError;
