import { h } from 'preact';
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'preact/hooks';
import PaymentLinkOptions from './PaymentLinkOptions';
import ReceiveSMSPayLink from './ReceiveSMSPayLink';
import styles from './paymentLink.css';
import {
  GeneratingInvoiceLayout,
  PaymentLinkCreated,
  PaymentLinkSMSSent,
} from './overlay';
import { GlobalsContext } from '../../../AppContext';
import { DeviceContext } from '../../../context/DeviceProvider';
import { useGeneratePaymentLink } from '../../../hooks/useGeneratePaymentLink';
import useLogger from '../../../hooks/useLogger';
import {
  PaymentCreatedEvent,
  PaymentErrorEvent,
  PaymentRequestPreparingEvent,
} from '../../../events/PaymentEvents';
import { CreateInvoiceResponse, MSOTreezPayCreatePaymentResponse } from '../../../models';
import { usePaymentStatusPolling } from '../../handlers/PaymentStatusPollingHandler';
import { getProcessorIcon } from '../../../utils/processorIcons';
import { useFlow } from '../../../hooks/useFlow';
import { IProcessorFlow, IProcessorFlowConfig } from '../../../types/processor';
import { ProcessorFlows, PaymentMethod } from '../../../constants';
import { getIntegrationTypeByFlow } from '../../../utils/processors';

interface SendPaymentProps {
  onSuccess?: (paymentLink: string | null) => void;
}

const ECommerceCreditPayments = () => {
  const {
    newTabRef,
    setPaymentProcessor,
    setOverlayVisible,
    setOverlayContext,
    setOverlayMessage,
    resetOverlay,
    paymentProcessor,
    payment,
    setPayment,
  } = useContext(GlobalsContext);
  const paymentMethod = PaymentMethod.Credit;

  const logger = useLogger();
  const deviceContext = useContext(DeviceContext);
  const [shouldSendPayment, setShouldSendPayment] =
    useState<SendPaymentProps | null>(null);
  const { generatePaymentLink } = useGeneratePaymentLink();
  const { stopPolling, startPolling } = usePaymentStatusPolling();

  const processorFlowConfig = useFlow(paymentMethod) as IProcessorFlowConfig[];

  const smsEnabledProcessors = useMemo(
    () =>
      processorFlowConfig
        .filter(({ flows }) => flows.includes(ProcessorFlows.PAYMENT_LINK_SMS))
        .map(({ processorName }) => processorName),
    [processorFlowConfig]
  );
  const redirectEnabledProcessors = useMemo(
    () =>
      processorFlowConfig
        .filter(({ flows }) => flows.includes(ProcessorFlows.PAYMENT_LINK_REDIRECT))
        .map(({ processorName }) => processorName),
    [processorFlowConfig]
  );

  if (
    smsEnabledProcessors.length === 0 &&
    redirectEnabledProcessors.length === 0
  ) {
    return <div className={styles.fallbackText}>Payment flows not enabled</div>;
  }

  if (!deviceContext) {
    return <div className={styles.fallbackText}>Loading...</div>;
  }

  if (!payment) {
    setOverlayVisible(true);
    setOverlayMessage(`Waiting for Payment Information`);
    setTimeout(() => {
      setOverlayVisible(false);
    }, 2000);
    return (
      <div className={styles.fallbackText}>Waiting for Payment Information</div>
    );
  }

  const sendPayment = useCallback(
    async ({ onSuccess }: SendPaymentProps) => {
      if (!paymentProcessor?.processorName || !paymentProcessor?.flow) return;

      const _processorName: string = paymentProcessor.processorName as string;
      const flowType = paymentProcessor.flow
      const integrationType = getIntegrationTypeByFlow(flowType);

      onLoading(flowType);

      const {
        originalAmount,
        employeeReferenceId,
        customer,
        ticketId,
        ticketAlphaNumId,
        handlePaymentResponse,
        integratedPaymentAttempts,
      } = payment;
      const paymentAttemptNumber = (integratedPaymentAttempts || 0) + 1;

      const requestPreparingEvent: PaymentRequestPreparingEvent = {
        eventType: 'PAYMENT_REQUEST_PREPARING',
        processorName: _processorName,
        paymentMethod,
        data: {
          originalAmount,
          employeeReferenceId,
          customer,
          ticketId,
          ticketAlphaNumId,
        },
      };

      handlePaymentResponse(requestPreparingEvent);

      try {
        stopPolling();

        const res = await generatePaymentLink(
          _processorName,
          paymentMethod,
          integrationType
        );

        setPayment({
          ...payment,
          invoiceId: res?.data.id,
          asyncUpdateReceived: false,
          integratedPaymentAttempts: paymentAttemptNumber,
        });

        const createdEvent: PaymentCreatedEvent = {
          eventType: 'PAYMENT_CREATED',
          processorName: _processorName,
          paymentMethod,
          data: res?.data as MSOTreezPayCreatePaymentResponse,
        };

        handlePaymentResponse(createdEvent);

        const paymentId = res?.data.id;

        const paymentLinkUrl =
          'paymentLinkUrls' in (res?.data || {})
            ? (res as CreateInvoiceResponse)?.data?.paymentLinkUrls?.[0]?.url ||
              null
            : null;
        if (
          paymentId &&
          ([ProcessorFlows.PAYMENT_LINK_QR, ProcessorFlows.PAYMENT_LINK_REDIRECT].includes(flowType) ? paymentLinkUrl : true)
        ) {
          onSuccess?.(paymentLinkUrl);
          startPolling(paymentId, 'invoice');
        }
      } catch (error) {
        logger.error('createPayment error:', error);
        const errorEvent: PaymentErrorEvent = {
          eventType: 'PAYMENT_ERROR',
          userMessage: error.data.message || 'Error creating payment.',
          processorName: _processorName,
          paymentMethod,
          data: {},
        };

        handlePaymentResponse(errorEvent);
        resetOverlay();
      }
    },
    [deviceContext, payment?.processorName, generatePaymentLink, resetOverlay]
  );

  const setCreditProcessor = (processorName: string, flow?: IProcessorFlow) => {
    setPaymentProcessor({
      paymentMethod,
      processorName,
      paymentSent: false,
      paymentDeviceConfigurationId: null,
      locationId: null,
      processorId: null,
      flow
    });
  };

  useEffect(() => {
    if (
      shouldSendPayment &&
      paymentProcessor?.processorName &&
      paymentProcessor.paymentMethod === paymentMethod
    ) {
      sendPayment(shouldSendPayment);
    }
  }, [paymentProcessor?.processorName, shouldSendPayment]);

  const onLoading = (flowType?: IProcessorFlow) => {
    setOverlayVisible(true);
    setOverlayContext({
      customComponent: (
        <GeneratingInvoiceLayout
          title={
            flowType === ProcessorFlows.PAYMENT_LINK_SMS
              ? 'Sending payment link...'
              : 'Generating payment link...'
          }
        />
      ),
    });
  };

  const handlePaymentLinkRedirect = (processorName: string) => {
    setCreditProcessor(processorName, ProcessorFlows.PAYMENT_LINK_REDIRECT);

    setShouldSendPayment({
      onSuccess: (url: string) => {
        setOverlayVisible(true);
        setOverlayContext({
          customComponent: (
            <PaymentLinkCreated
              processor={processorName}
              paymentMethod={paymentMethod}
            />
          ),
        });
        const urlMatch = url.match(/https?:\/\/[^\s]+/);
        const parsedUrl = urlMatch ? urlMatch[0] : null;
        if (parsedUrl) {
          newTabRef.current.tab = window.open(parsedUrl, '_blank');
          newTabRef.current.url = parsedUrl;
        } else {
          logger.error(
            `Error: Payment link extraction failed. url=${url}`,
            url,
            payment,
            paymentProcessor
          );
        }
      },
    });
  };

  const handleReceiveSMSPaylink = (processorName: string) => {
    setCreditProcessor(processorName, ProcessorFlows.PAYMENT_LINK_SMS);

    setShouldSendPayment({
      onSuccess: () => {
        setOverlayVisible(true);
        setOverlayContext({
          customComponent: <PaymentLinkSMSSent />,
        });
      },
    });
  };

  return (
    <div>
      <ReceiveSMSPayLink
        options={smsEnabledProcessors.map(processor => ({
          label: `Pay now with ${!getProcessorIcon(processor) ? processor : ''}`,
          onClick: () => handleReceiveSMSPaylink(processor),
          key: `${processor}-sms`,
          icon: getProcessorIcon(processor),
        }))}
      />

      {smsEnabledProcessors.length > 0 &&
        redirectEnabledProcessors.length > 0 && (
          <hr className={styles.creditOptionsHR} />
        )}

      <PaymentLinkOptions
        options={redirectEnabledProcessors.map(processor => ({
          label: `Pay now with ${!getProcessorIcon(processor) ? processor : ''}`,
          onClick: () => handlePaymentLinkRedirect(processor),
          key: `${processor}-redirect`,
          icon: getProcessorIcon(processor),
        }))}
      />
    </div>
  );
};

export default ECommerceCreditPayments;
