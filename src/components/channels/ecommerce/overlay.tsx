import { Fragment, h } from 'preact';
import styles from './overlay.css';
import { useContext } from 'preact/hooks';
import { GlobalsContext } from '../../../AppContext';
import Loader from './Loader';
import { capitalize, formatUSPhoneNumber } from '../../../utils/stringUtils';
import Button from '../../buttons/button';
import { getProcessorIcon } from '../../../utils/processorIcons';
import { ECommCardIcon } from '../../icons/ecomm';
import closeIcon from '../../../assets/close.svg';
import chatIcon from '../../../assets/chat.svg';
interface ModalProps {
  title?: preact.ComponentChildren;
  children?: preact.ComponentChildren;
  onClose?: () => void;
}

export const OverlayModal: preact.FunctionComponent<ModalProps> = ({
  title,
  children,
  onClose,
}) => {
  const { resetOverlay } = useContext(GlobalsContext);

  return (
    <div className={styles.modalContainer}>
      <div className={styles.modalHeader}>
        {title && <div>{title}</div>}
        <button className={styles.modalClose} onClick={onClose || resetOverlay}>
          <img src={closeIcon} alt="close modal" />
        </button>
      </div>
      {children && <div className={styles.modalBody}>{children}</div>}
    </div>
  );
};

export const GeneratingInvoiceLayout = ({ title }: { title?: string }) => {
  return (
    <OverlayModal
      children={
        <Fragment>
          <Loader />
          <div className={styles.generatingInvoice}>
            {title || 'Generating payment link...'}
          </div>
        </Fragment>
      }
    />
  );
};

export const PaymentLinkCreated = ({
  processor,
  paymentMethod,
}: {
  processor: string;
  paymentMethod: string;
}) => {
  const { newTabRef } = useContext(GlobalsContext);
  const isCredit = paymentMethod === 'Credit';
  const titleIcon = isCredit ? (
    <ECommCardIcon />
  ) : (
    <img
      src={getProcessorIcon(processor.toLowerCase())}
      className={styles.paymentLinkTitleIcon}
    />
  );

  return (
    <OverlayModal
      title={
        <div className={styles.paymentLinkTitle}>
          {titleIcon}
          {isCredit && (
            <div className={styles.paymentLinkTitleText}>Credit card</div>
          )}
        </div>
      }
      children={
        <div className={styles.paymentLinkBody}>
          <div>
            Don’t see {capitalize(processor.toLowerCase())}’s secure browser?
            We’ll help you relaunch the window to complete your purchase.
          </div>
          <Button
            text="Click to continue"
            variant="primary"
            onClick={() => {
              if (newTabRef.current.tab && !newTabRef.current.tab.closed) {
                newTabRef.current.tab.focus();
              } else if (newTabRef.current.url) {
                newTabRef.current.tab = window.open(
                  newTabRef.current.url,
                  '_blank'
                );
              }
            }}
          />
        </div>
      }
    />
  );
};

export const PaymentLinkSMSSent = () => {
  const { payment } = useContext(GlobalsContext);
  const customerPhoneNumber = payment?.customer?.phone;

  return (
    <OverlayModal
      title={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
          }}
        >
          <img src={chatIcon} alt="SMS icon" />
          <div>We’ve sent you a text</div>
        </div>
      }
      children={
        <Fragment>
          <div className={styles.paymentLinkBody}>
            <div>
              Just click on the payment link in the message to complete your
              purchase.{' '}
              {customerPhoneNumber && (
                <>
                  It was sent to{' '}
                  <strong>{formatUSPhoneNumber(customerPhoneNumber)}</strong>.
                </>
              )}
            </div>
            <div>
              Didn’t get the text? Please make sure your phone number is correct
              in the profile section.
            </div>
          </div>
        </Fragment>
      }
    />
  );
};
