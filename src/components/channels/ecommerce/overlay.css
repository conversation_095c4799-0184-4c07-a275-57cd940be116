@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modalContainer {
  position: fixed;
  background: #fff;
  box-shadow: 0px 20px 25px -5px rgba(0, 0, 0, 0.10), 0px 8px 10px -6px rgba(0, 0, 0, 0.10);
  z-index: 100;
  padding: 24px 16px 32px 16px;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--Color-Grey-Blue-80, #333D4E);
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
}

.modalClose {
  background: none;
  border: none;
  color: #808690;
  margin: 0;
  padding: 0;
  width: 24px;
  height: 24px;
  position: absolute;
  top: 24px;
  right: 16px;
}

.modalBody {
  font-size: 14px;
  color: #444;
  margin-top: 32px;
}

/* desktop view */
@media (min-width: 1024px) {
  .modalContainer {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 480px;
    border-radius: 16px;
  }
}
/* mobile view */
@media (max-width: 1023px) {
  .modalContainer {
    bottom: 0px;
    width: 100vw;
    border-radius: 12px 12px 0 0;
    animation: slideUp 0.4s ease-out;
  }
}

.generatingInvoice {
  color: var(--Color-Grey-Blue-80, #333D4E);
  text-align: center;
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
  margin-top: 32px;
  padding-left: 12px;
  padding-right: 12px;
}

.paymentLinkBody {
  color: var(--Color-Grey-Blue-70, #4D5664);
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.paymentLinkBody strong {
  font-weight: 500;
}

.paymentLinkTitle {
  display: flex;
  align-items: center;
}

.paymentLinkTitleText {
  margin-left: 8px;
}

img.paymentLinkTitleIcon {
  height: 25px;
}
