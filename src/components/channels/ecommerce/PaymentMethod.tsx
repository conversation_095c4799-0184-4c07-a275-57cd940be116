import { Fragment, h } from 'preact';
import styles from './paymentMethod.css';
import { ECommACHIcon, ECommCardIcon } from '../../icons/ecomm';
import clsx from 'clsx';

const PaymentMethod = ({
  method,
  selected = false,
}: {
  method: string;
  selected?: boolean;
}) => {
  const config = {
    ACH: {
      label: <span className={clsx(styles.mainLabel)}>Pay by Bank</span>,
      icon: <ECommACHIcon className={styles.icon} />,
    },
    Credit: {
      label: <span className={styles.mainLabel}>Pay by Card</span>,
      icon: <ECommCardIcon className={clsx(styles.icon)} />,
    },
  };
  const methodConfig = config?.[method];

  if (!methodConfig) return <Fragment />;

  return (
    <div className={clsx(styles.container, selected && styles.methodSelected)}>
      {methodConfig?.icon}
      <span className={styles.text}>{methodConfig?.label}</span>
    </div>
  );
};

export default PaymentMethod;
