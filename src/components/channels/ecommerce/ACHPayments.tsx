import { h } from 'preact';
import AchPaymentsAdapter from '../../adapters/achPayments';
import PaymentLinkOptions from './PaymentLinkOptions';
import { GeneratingInvoiceLayout, PaymentLinkCreated } from './overlay';

const ECommerceACHPayments = () => {
  return (
    <AchPaymentsAdapter
      renderPaymentOptions={options => {
        const paylinkOptions = options.map(option => ({
          ...option,
          label: 'Pay now with',
        }));
        return <PaymentLinkOptions options={paylinkOptions} />;
      }}
      renderOverlay={{
        onGeneratingPaylink: () => <GeneratingInvoiceLayout />,
        onPaylinkCreated: processorName => (
          <PaymentLinkCreated processor={processorName} paymentMethod={'ACH'} />
        ),
      }}
      openInNewTab
      autoSelectSingleProcessor={false}
    />
  );
};

export default ECommerceACHPayments;
