import { h } from 'preact';
import AchPaymentsAdapter, {
  IAchPaymentOptions,
} from '../../adapters/achPayments';
import PaymentOptionsContainer from '../../payments/PaymentOptionsContainer';
import {
  GeneratingQrCodeLayout,
  PaymentErrorLayout,
} from '../../../layout/OverlayCustomComponents';
import { capitalize } from '../../../utils/stringUtils';
import Button from '../../buttons/button';
import { useContext } from 'preact/hooks';
import { GlobalsContext } from '../../../AppContext';
import useResetPayment from '../../../hooks/useResetPayment';

const FFDAchPayments = () => {
  const { navigate, setApiError, resetOverlay } = useContext(GlobalsContext);
  const resetPayment = useResetPayment();

  const getRenderConfig = (payOptions: IAchPaymentOptions[]) => {
    const options = payOptions.map(({ fee, onClick, name, icon, id }) => ({
      label: '',
      fee,
      icon,
      onClick,
      iconStyles: {
        width: 'auto',
        height: 'auto',
      },
      bgColor: '#fff',
      id,
    }));

    return options;
  };

  return (
    <AchPaymentsAdapter
      renderPaymentOptions={paymentOptions => (
        <PaymentOptionsContainer
          paymentOptions={getRenderConfig(paymentOptions)}
        />
      )}
      renderOverlay={{
        onGeneratingPaylink: () => <GeneratingQrCodeLayout />,
        onError: error => (
          <PaymentErrorLayout
            errorMessage={capitalize(
              error.data?.processorResponseDetailMessage ||
                'An unexpected error occurred'
            )}
            subErrorMessage={
              error.data?.displayErrorMessage ||
              'Error creating payment. See logs.'
            }
            secondaryActionButton={
              <Button
                variant={'secondary'}
                text={'Change payment method'}
                onClick={() => {
                  resetOverlay();
                  setApiError(null);
                  resetPayment();
                  navigate('/');
                }}
              />
            }
          />
        ),
      }}
    />
  );
};

export default FFDAchPayments;
