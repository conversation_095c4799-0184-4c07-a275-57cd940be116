import { h } from 'preact';
import atmIcon from '../../../assets/atm.svg';
import RotatingGradientCircle from '../../progress/RotatingGradientCircle';

const FFDRotatingGradient = () => {
  return (
    <RotatingGradientCircle
      gradientColors={{
        stop1: 'rgb(145, 206, 88)',
        stop2: 'rgb(210, 245, 176)',
        stop3: 'rgb(210, 245, 176)',
      }}
      size="50vw"
      maxSize="260px"
      icon={atmIcon}
    />
  );
};

export default FFDRotatingGradient;
