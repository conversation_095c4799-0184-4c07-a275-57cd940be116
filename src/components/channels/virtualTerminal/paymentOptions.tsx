import { h } from 'preact';
import atmIcon from '../../../assets/vt-atm.svg';
import debitIcon from '../../../assets/vt-debit.svg';
import creditIcon from '../../../assets/vt-credit.svg';
import achIcon from '../../../assets/vt-ach.svg';
import crossIcon from '../../../assets/close.svg';
import { useContext, useMemo } from 'preact/hooks';
import { DeviceContext } from '../../../context/DeviceProvider';
import { GlobalsContext } from '../../../AppContext';
import { generatePaymentOptionSelectedEvent, PaymentCloseEvent } from '../../../events/PaymentEvents';
import { useEventDispatcher } from '../../../context/EventDispatcherProvider';
import useLogger from '../../../hooks/useLogger';
import styles from './styles.css';
import { toDollars } from '../../../utils/currencyUtils';
import { PreviewContext } from '../../../context/PreviewProvider';
import NavButton from '../../navbutton/navButton';

const PaymentOptions = () => {
  const logger = useLogger();
  const { navigate, setOverlayContext, payment, setWidgetOpen, paymentProcessor } = useContext(GlobalsContext);
  const { dispatchEvent } = useEventDispatcher();
  const previewContext = useContext(PreviewContext);

  const totalAmount = useMemo(() => {
    if (previewContext?.previewTotals?.totalAmount) {
      return Number(previewContext.previewTotals.totalAmount);
    }
    return payment?.originalAmount || 0;
  }, [previewContext, payment]);

  const deviceContext = useContext(DeviceContext);
  if (!deviceContext) {
    return <div>Loading...</div>;
  }

  const handleCloseButton = () => {
    setWidgetOpen(false);
    const closeEvent: PaymentCloseEvent = {
      eventType: 'PAYMENT_CLOSE',
      userMessage: 'Widget closed by user',
      data: {},
      processorName: payment?.processorName || '',
      paymentMethod: paymentProcessor?.paymentMethod || '',
    };
    payment?.handlePaymentResponse(closeEvent);
  };

  const { paymentMethods, loading } = deviceContext;

  const paymentMethodOptions: Record<string, any> = {
    Debit: {
      label: 'Charge Debit',
      icon: debitIcon,
      onClick: () => {
        setOverlayContext(null);
        logger.debug('Debit Selected');
        if (loading) {
          return;
        }
        dispatchEvent(
          'paymentOptionSelected',
          generatePaymentOptionSelectedEvent('Debit')
        );
        navigate('/debit');
      },
    },
    ATM: {
      label: 'Charge ATM',
      icon: atmIcon,
      onClick: () => {
        setOverlayContext(null);
        logger.debug('ATM Selected');
        if (loading) {
          return;
        }
        dispatchEvent(
          'paymentOptionSelected',
          generatePaymentOptionSelectedEvent('Rounded Debit')
        );
        navigate('/atm');
      },
    },
    ACH: {
      label: 'Charge ACH',
      icon: achIcon,
      onClick: () => {
        setOverlayContext(null);
        logger.debug('ACH Selected');
        if (loading) {
          return;
        }
        dispatchEvent(
          'paymentOptionSelected',
          generatePaymentOptionSelectedEvent('ACH')
        );
        navigate('/ach');
      },
    },
    Credit: {
      label: 'Charge Credit',
      icon: creditIcon,
      onClick: () => {
        setOverlayContext(null);
        logger.debug('Credit Selected');
        dispatchEvent(
          'paymentOptionSelected',
          generatePaymentOptionSelectedEvent('Credit')
        );
        navigate('/credit');
      },
    },
  };

  const options = Object.keys(paymentMethods)
    .map((paymentMethod: string) => paymentMethodOptions[paymentMethod])
    .sort((a, b) => a.order - b.order);

  return (
    <div>
      <NavButton onClick={handleCloseButton} icon={crossIcon} />
      <div className={styles.paymentAmount}>${toDollars(totalAmount)}</div>
      <div className={styles.paymentMethodsHeader}>Payment methods</div>
      <div className={styles.container}>
        {options.map(({ label, icon, onClick }) => (
          <button key={label} className={styles.button} onClick={onClick}>
            <img src={icon} alt={label} className={styles.icon} />
            <span className={styles.label}>{label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default PaymentOptions;
