import { h } from 'preact';
import {
  VTCapturingFailedDetailedLayout,
  VTCapturingFailedLayout,
  VTCapturingInProgressLayout,
  VTCapturingSuccessLayout,
  VTPaymentApprovedLayout,
} from '../../../layout/OverlayCustomComponents';
import { useContext, useEffect } from 'preact/hooks';
import {
  ConfigContext,
  GlobalsContext,
  ServiceContext,
} from '../../../AppContext';
import useLogger from '../../../hooks/useLogger';
import { PaymentCapturedEvent, PaymentErrorEvent } from '../../../events/PaymentEvents';

interface OverlayProps {
  paymentMethod: string;
  processorName: string;
  paymentChargeId?: string;
}

const CaptureCharge = ({ paymentMethod, processorName, paymentChargeId }: OverlayProps) => {
  const { payment, setOverlayContext } = useContext(GlobalsContext);

  const service = useContext(ServiceContext);
  const config = useContext(ConfigContext);
  const logger = useLogger();



  useEffect(() => {
    const processCaptureCharge = async () => {
      setOverlayContext({
        customComponent: (
          <VTCapturingInProgressLayout />
        ),
        disableButtonsOnProgress: true,
      });


      try {
        const res = await service?.captureCharge({
          ticketId: payment?.ticketId,
          ticketAlphaNumId: payment?.ticketAlphaNumId,
          feeAmount: payment?.feeAmount,
          referenceNumber: payment?.ticketAlphaNumId,
          processorName: processorName,
          originalAmount: payment?.originalAmount,
          employeeReferenceId: payment?.employeeReferenceId,
          isSync: true,
          customer: payment?.customer,
          transactingAssociateId: payment?.transactingAssociateId,
          paymentChargeId: paymentChargeId,
        });

        if (res?.data?.id) {
            const capturedEvent: PaymentCapturedEvent = {
                eventType: 'PAYMENT_CAPTURED',
                userMessage: 'Payment captured successfully',
                processorName,
                paymentMethod,
                data: {
                    paymentId: res?.data?.id,
                    ...res?.data
                },
            };

            payment?.handlePaymentResponse(capturedEvent);

          setOverlayContext({
            customComponent: (
              <VTCapturingSuccessLayout
                totalAmount={res?.data?.originalAmount}
                cashbackAmount={res?.data?.cashbackAmount}
                tipAmount={res?.data?.tipAmount}
              />
            ),
            disableButtonsOnProgress: true,
          });
          setTimeout(() => {
            setOverlayContext({
              customComponent: (
                <VTPaymentApprovedLayout
                  totalAmount={res?.data?.originalAmount}
                  cashbackAmount={res?.data?.cashbackAmount}
                  tipAmount={res?.data?.tipAmount}
                  createdAt={res?.data?.createdAt}
                  feeAmount={res?.data?.feeAmount}
                  paymentMethod={paymentMethod}
                  providerName={processorName}
                  entityName={payment?.entityName}
                  channel={config.channel}
                />
              ),
              disableButtonsOnProgress: true,
            });
          }, 3000);
        }
      } catch (error) {
        logger.error('captureCharge error:', error);

        const errorEvent: PaymentErrorEvent = {
          eventType: 'PAYMENT_ERROR',
          userMessage: 'Error capturing charge. See logs.',
          processorName,
          paymentMethod,
          data: {
            paymentId: payment?.ticketId,
          },
        };

        payment?.handlePaymentResponse(errorEvent);

        setOverlayContext({
          customComponent: <VTCapturingFailedLayout />,
        });

        setTimeout(() => {
          setOverlayContext({
            customComponent: (
              <VTCapturingFailedDetailedLayout
                paymentMethod={paymentMethod}
                processorName={processorName}
                paymentChargeId={paymentChargeId}
              />
            ),
            disableButtonsOnProgress: true,
          });
        }, 800);
      }
    };

    processCaptureCharge();
  }, []);

  return null
};

export default CaptureCharge;
