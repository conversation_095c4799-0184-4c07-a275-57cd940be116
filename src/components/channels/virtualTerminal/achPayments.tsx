import { h } from 'preact';
import styles from './styles.css';
import clsx from 'clsx';
import AchPaymentsAdapter from '../../adapters/achPayments';
import { toDollars } from '../../../utils/currencyUtils';
import { useContext, useMemo } from 'preact/hooks';
import { GlobalsContext } from '../../../AppContext';
import { PreviewContext } from '../../../context/PreviewProvider';
import {
  VTPaymentLoader,
  PaymentErrorLayout,

} from '../../../layout/OverlayCustomComponents';
import { capitalize } from '../../../utils/stringUtils';
import Button from '../../buttons/button';
import useResetPayment from '../../../hooks/useResetPayment';



const VirtualTerminalAchPayments = () => {
  const { payment,navigate, setApiError, resetOverlay } = useContext(GlobalsContext);
  const previewContext = useContext(PreviewContext);
  const resetPayment = useResetPayment();
  
  const totalAmount = useMemo(() => {
    if (previewContext?.previewTotals?.totalAmount) {
      return Number(previewContext.previewTotals.totalAmount);
    }
    return payment?.originalAmount || 0;
  }, [previewContext, payment]);

  
  return (
    <AchPaymentsAdapter
      renderPaymentOptions={paymentOptions => (
        <div className={clsx(styles.container, styles.containerAch)}>
          <div className={styles.paymentAmountACH}>${toDollars(totalAmount)}</div>
          <div className={styles.paymentMethodsHeader}>Choose ACH Provider</div>
          {paymentOptions.map(({ name, icon, onClick }) => (
            <button
              key={name}
              className={clsx(styles.button, styles.buttonAch)}
              onClick={onClick}
            >
              <img src={icon} alt={name} />
            </button>
          ))}
        </div>
      )}
      renderOverlay={{
        onGeneratingPaylink: () => (
          <VTPaymentLoader
            header="Generating QR code"
            subtext="Hold tight! Almost there..."
            showCancel={false}
            showWarningBanner={false}
          />
        ),
        onError: error => (
          <PaymentErrorLayout
            errorMessage={capitalize(
              error.data?.processorResponseDetailMessage ||
                'An unexpected error occurred'
            )}
            subErrorMessage={
              error.data?.displayErrorMessage ||
              'Error creating payment. See logs.'
            }
            secondaryActionButton={
              <Button
                variant={'secondary'}
                text={'Change payment method'}
                onClick={() => {
                  resetOverlay();
                  setApiError(null);
                  resetPayment();
                  navigate('/');
                }}
              />
            }
          />
        ),
      }}
    />
  );
};

export default VirtualTerminalAchPayments;
