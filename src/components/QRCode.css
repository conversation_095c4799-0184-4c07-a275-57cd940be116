.payStatement {
  color: var(--tz-text-color-primary);
  text-align: center;
  font-size: 24px;
  font-weight: 400;
  line-height: 32px;
}

.centerAlign {
  display: flex;
  text-align: center;
  justify-content: center;
}

.subtext {
  font-size: 24px;
  margin-bottom: 8px;
  margin-top: 8px;
}

.margin {
  margin-top: 32px;
  margin-bottom: 72px;
}

.canvas {
  color: #1a4007;
  background-color: #1a4007;
  padding-top: 16px;
  padding-right: 16px;
  padding-left: 16px;
  border-radius: 40px;
  text-align: center;
  justify-content: center;
  box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.1);
}

p.text {
  color: var(--bw-primary-white, #fff);
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  margin-top: 16px;
  margin-bottom: 16px;
}

.canvasRadius {
  border-radius: 24px;
}

.amountText {
  font-weight: 600;
  padding-left: 10px;
  color: var(--Color-Grey-Blue-80, #333d4e);
  font-size: 24px;
  line-height: 32px;
}

.vtContainer {
  padding-top: 24px;
}

.vtLoader {
  filter: grayscale(100%) brightness(60%) contrast(110%);
}

.vtCanvas.canvas {
  color: #001e50;
  background-color: #001e50;
}

.goBackButton {
  width: 440px;
  display: flex;
  height: 64px;
  padding: 12px 16px;
  gap: 4px;
  border-radius: 4px;
  border: 1px solid var(--Color-Grey-Blue-20, #cccfd3);
  box-shadow: 0px 1px 2px 0px rgba(0, 13, 34, 0.07);

  color: var(--Color-Grey-Blue-80, #333d4e);
  font-size: 18px;
  line-height: 28px;
}
