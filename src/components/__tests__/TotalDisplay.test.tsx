import { h } from 'preact';
import { render, screen, fireEvent } from '@testing-library/preact';
import '@testing-library/jest-dom';
import { GlobalsContext } from '../../AppContext';
import { PreviewContext } from '../../context/PreviewProvider';
import TotalDisplay from '../totalDisplay';
import { act } from 'preact/test-utils';
// Import mocks
import {
  mockGlobalsContext,
  resetMockGlobalsContext,
} from '../../mocks/mockGlobalsContext';
import {
  mockPreviewContext,
  resetMockPreviewContext,
} from '../../mocks/mockPreviewContext';

describe('TotalDisplay Component', () => {
  beforeEach(() => {
    // Reset mocks before each test if necessary
    resetMockGlobalsContext();
    resetMockPreviewContext();
  });

  it('renders without crashing', () => {
    render(
      <GlobalsContext.Provider value={mockGlobalsContext}>
        <PreviewContext.Provider value={mockPreviewContext}>
          <TotalDisplay />
        </PreviewContext.Provider>
      </GlobalsContext.Provider>
    );

    expect(screen.getByText(/Estimated Total:/i)).toBeInTheDocument();
  });

  it('displays the correct estimated total amount', () => {
    render(
      <GlobalsContext.Provider value={mockGlobalsContext}>
        <PreviewContext.Provider value={mockPreviewContext}>
          <TotalDisplay />
        </PreviewContext.Provider>
      </GlobalsContext.Provider>
    );

    expect(screen.getByText('$12.50')).toBeInTheDocument();
  });

  it('expands breakdown when header is clicked', () => {
    render(
      <GlobalsContext.Provider value={mockGlobalsContext}>
        <PreviewContext.Provider value={mockPreviewContext}>
          <TotalDisplay />
        </PreviewContext.Provider>
      </GlobalsContext.Provider>
    );

    const header = screen.getByRole('button', { name: /Estimated Total:/i });
    fireEvent.click(header);

    expect(screen.getByText('Original Price')).toBeInTheDocument();
    expect(mockGlobalsContext.setOverlayVisible).toHaveBeenCalledWith(
      expect.any(Function)
    );
  });

  it('displays correct breakdown details when expanded', () => {
    render(
      <GlobalsContext.Provider value={mockGlobalsContext}>
        <PreviewContext.Provider value={mockPreviewContext}>
          <TotalDisplay />
        </PreviewContext.Provider>
      </GlobalsContext.Provider>
    );

    const header = screen.getByRole('button', { name: /Estimated Total:/i });
    fireEvent.click(header);

    expect(screen.getByText('Original Price')).toBeInTheDocument();
    expect(screen.getByText('$10.00')).toBeInTheDocument();

    expect(screen.getByText('Total Discounts')).toBeInTheDocument();
    expect(screen.getByText('-$1.00')).toBeInTheDocument();

    expect(screen.getByText('Total Tax')).toBeInTheDocument();
    expect(screen.getByText('$0.50')).toBeInTheDocument();

    expect(screen.getByText('Tip')).toBeInTheDocument();
    expect(screen.getByText('$2.00')).toBeInTheDocument();
  });

  it.skip('displays rounding info when payment method is ATM and on tips view', () => {
    render(
      <GlobalsContext.Provider value={mockGlobalsContext}>
        <PreviewContext.Provider value={mockPreviewContext}>
          <TotalDisplay />
        </PreviewContext.Provider>
      </GlobalsContext.Provider>
    );

    expect(
      screen.getByText((content, node) => {
        const hasText = (node: Element) =>
          node.textContent === 'Rounded to nearest $5.00';
        //@ts-ignore
        const nodeHasText = hasText(node);
        const childrenDontHaveText = Array.from(node?.children || []).every(
          child => !hasText(child)
        );
        return nodeHasText && childrenDontHaveText;
      })
    ).toBeInTheDocument();
  });

  it.skip('auto-collapses after 5 seconds when expanded', () => {
    jest.useFakeTimers();

    render(
      <GlobalsContext.Provider value={mockGlobalsContext}>
        <PreviewContext.Provider value={mockPreviewContext}>
          <TotalDisplay />
        </PreviewContext.Provider>
      </GlobalsContext.Provider>
    );

    const header = screen.getByRole('button', { name: /Estimated Total:/i });
    fireEvent.click(header);

    expect(screen.getByText('Original Price')).toBeInTheDocument();

    act(() => {
      jest.advanceTimersByTime(21000);
    });

    expect(screen.queryByText('Original Price')).not.toBeInTheDocument();

    jest.useRealTimers();
  });

  it.skip('collapses when header is clicked again', () => {
    jest.useFakeTimers();

    render(
      <GlobalsContext.Provider value={mockGlobalsContext}>
        <PreviewContext.Provider value={mockPreviewContext}>
          <TotalDisplay />
        </PreviewContext.Provider>
      </GlobalsContext.Provider>
    );

    const header = screen.getByRole('button', { name: /Estimated Total:/i });
    fireEvent.click(header);

    expect(screen.getByText('Original Price')).toBeInTheDocument();

    fireEvent.click(header);

    act(() => {
      jest.advanceTimersByTime(21000);
    });

    expect(screen.queryByText('Original Price')).not.toBeInTheDocument();
  });
});
