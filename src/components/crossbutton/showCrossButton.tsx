import { h } from 'preact';
import { useContext, useEffect, useState } from 'preact/hooks';

import { GlobalsContext } from '../../AppContext';
import NavButton from '../navbutton/navButton';
import crossIcon from '../../assets/close.svg';

const ShowCloseButton = ({ onClose }: { onClose: () => void }) => {
  const { currentPath, history } = useContext(GlobalsContext);

  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    const hasHistory = history.length > 1;
    const isExcludedPath = [
      '/qr',
      '/tips',
      '/summary',
      '/paymentProcessing',
      '/atm',
      '/debit',
      '/credit',
    ].some(path => currentPath.startsWith(path));
    setShouldShow(hasHistory && !isExcludedPath);
  }, [currentPath, history]);

  return shouldShow ? <NavButton onClick={() => onClose()} icon={crossIcon} /> : null;
};

export default ShowCloseButton;
