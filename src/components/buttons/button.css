.base {
  padding: 20px 32px;
  font-weight: 500;
  font-size: 24px;
  line-height: 32px;
  border-radius: 16px;
  cursor: pointer;
  text-align: center;
  justify-content: center;
  display: flex;
  width: 100%;
  margin: 0;
}

.primary {
  composes: base;
  color: var(--tz-text-color-primary);
  background-color: var(--tz-color-primary);
  border: none;
}

.primary:hover {
  background-color: var(--tz-color-primary-hover);
}
.primary:disabled {
  background-color: var(--tz-color-disabled);
  cursor: not-allowed;
  box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.07) inset, 0px 1px 2px 0px rgba(0, 0, 0, 0.09);
}

.secondary {
  composes: base;
  background-color: #ffffff;
  color: #1a4007;
  border: 2px var(--tz-color-border) solid;
}

.secondary:hover {
  background-color: #f5f5f6;
}

.danger {
  composes: base;
  background-color: #ffffff;
  color: var(--tz-color-error);
  border: 2px var(--tz-color-border) solid;
}

.content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  padding-left: 8px;
}

.ecommerce.primary {
  border-radius: 8px;
  border: 1px solid var(--tz-color-border);
  background: #FFF;
  box-shadow: 0px 1px 2px 0px rgba(0, 13, 34, 0.07);
  color: var(--Color-Grey-Blue-90, #1A2538);
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

.ecommerce.primary:hover {
  background: #FFF;
}

.ecommerce .icon {
  height: 20px;
}
