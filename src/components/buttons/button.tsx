import { h } from 'preact';
import styles from './button.css';
import clsx from 'clsx';
import { useContext } from 'preact/hooks';
import { ConfigContext } from '../../AppContext';
import { Channel } from '../../models';

interface IButtonProps {
  onClick: () => void;
  variant: string;
  text: string;
  disabled?: boolean;
  icon?: any;
  style?: string;
  keyDown?: () => void;
  className?: string;
}

const Button = ({
  onClick,
  disabled = false,
  variant,
  text,
  icon,
  keyDown,
  style,
  className,
}: IButtonProps) => {
  const { channel } = useContext(ConfigContext);

  const channelCls = {
    [Channel.VIRTUAL_TERMINAL]: '',
    [Channel.FFD]: '',
    [Channel.ECOMMERCE]: styles.ecommerce,
  }?.[channel ?? Channel.FFD];

  return (
    <button
      tabIndex={0}
      className={clsx(styles.base, styles[variant], channelCls, className)}
      disabled={disabled}
      style={style}
      onClick={disabled ? undefined : onClick}
      onKeyDown={e => e.key === 'Enter' && keyDown}
    >
      <span className={styles.content}>
        {text}
        {icon && <img src={icon} className={styles.icon} />}
      </span>
    </button>
  );
};

export default Button;
