import { useContext, useEffect } from 'preact/hooks';
import { DeviceContext } from '../../context/DeviceProvider';
import { FunctionalComponent } from 'preact';
import { GlobalsContext } from '../../AppContext';
import useLogger from '../../hooks/useLogger';

const WidgetEventListener: FunctionalComponent = () => {
  const logger = useLogger();

  const deviceContext = useContext(DeviceContext);
  const globalContext = useContext(GlobalsContext);

  if (!globalContext) {
    return null;
  }

  const { payment, element, setMountedComponents } = globalContext;

  useEffect(() => {
    const handleEvent = (e: CustomEvent) => {
      const { name, detail: eventPayload } = (
        e as CustomEvent<{ name?: string; detail: any }>
      ).detail;

      switch (name) {
        case 'refresh': {
          if (deviceContext) {
            deviceContext?.refetch();
          } else {
            logger.error('deviceContext is null or undefined');
          }
          break;
        }
        case 'paymentOptionSelected': {
          if (payment?.handlePaymentResponse) {
            payment.handlePaymentResponse(eventPayload);
          } else {
            logger.warn(
              'handlePaymentResponse callback function is not present.'
            );
          }
          break;
        }
        case 'payment': {
          // ignore, its been handled
          break;
        }
        case 'mount': {
          const { component, element } = e.detail.args[1];
          if (!component) {
            logger.warn('mount event received without a component name.');
            return;
          }
          setMountedComponents(prev => ({
            ...prev,
            [component]: { component, element },
          }));
          logger.log(`Mounted component: ${component}`);
          break;
        }
        case 'unmount': {
          const { component } = e.detail.args[1];
          if (!component) {
            logger.warn('unmount event received without a component name.');
            return;
          }
          setMountedComponents(prev => {
            const updated = { ...prev };
            delete updated[component];
            return updated;
          });
          logger.log(`Unmounted component: ${component}`);
          break;
        }
        default:
          logger.warn(
            `WidgetEventListener received en event it didn't know how to handle ${JSON.stringify(e.detail)}`
          );
          break;
      }
    };

    element?.addEventListener('widget-event', handleEvent);

    return () => {
      element?.removeEventListener('widget-event', handleEvent);
    };
  }, [element, deviceContext]);

  return null; // This component only listens for events, no UI needed
};

export default WidgetEventListener;
