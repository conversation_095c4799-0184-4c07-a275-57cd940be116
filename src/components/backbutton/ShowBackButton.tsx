import { h } from 'preact';
import { useContext, useEffect, useState } from 'preact/hooks';
import { GlobalsContext } from '../../AppContext';
import NavButton from '../navbutton/navButton';
import backIcon from '../../assets/backButton.svg';

const ShowBackButton = () => {
  const { navigate, currentPath, history } = useContext(GlobalsContext);

  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    const hasHistory = history.length > 1;
    const isExcludedPath = [
      '/qr',
      '/tips',
      '/summary',
      '/paymentProcessing',
    ].some(path => currentPath.startsWith(path));
    setShouldShow(hasHistory && !isExcludedPath);
  }, [currentPath, history]);

  return shouldShow ? <NavButton onClick={() => navigate(-1)} icon={backIcon} /> : null;
};

export default ShowBackButton;
