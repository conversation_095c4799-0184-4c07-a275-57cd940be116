.container {
  position: absolute;
  top: 56px;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  flex-direction: column;
}

.vtContainer.container {
  top: 0px;
}

.lightBg {
  background-color: #fff;
}

.darkBg.container {
  background-color: rgb(0,0,0,0.1);
}

.message {
  color: var(--tz-text-color-primary);
  font-size: 24px;
  text-align: center;
  margin-top: 10px;
}

.icon {
  width: 100px;
  height: 100px;
  object-fit: contain;
}

.buttonContainer {
  display: inline-block;
  height: 50px;
}

.button {
  margin: 0;
  padding: 10px 20px;
  background-color: #fff;
  color: var(--tz-text-color-primary);
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  display: inline-block;
  outline: none;
}

.buttonIcon {
  margin-right: 5px;
}

.ecommContainer.container {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: block;
}
