import { PaymentMethod, ProcessorFlows, ProcessorIntegrationType } from '../constants'

export type IPaymentMethod = typeof PaymentMethod[keyof typeof PaymentMethod];

export type IProcessorFlow = typeof ProcessorFlows[keyof typeof ProcessorFlows];

export type IProcessorIntegrationType = typeof ProcessorIntegrationType[keyof typeof ProcessorIntegrationType];

export interface IProcessorFlowConfig {
    processorName: string
    flows: IProcessorFlow[]
}

export type IProcessorFlowConfigByMethod = Record<IPaymentMethod, IProcessorFlowConfig[]>
