export type PaymentEventType =
  | 'PAYMENT_REQUEST_PREPARING'
  | 'PAYMENT_CREATED'
  | 'PAYMENT_LINK_CREATED'
  | 'PAYMENT_CANCELED'
  | 'PAYMENT_ERROR'
  | 'PAYMENT_SUBMITTED'
  | 'PAYMENT_APPROVED'
  | 'PAYMENT_CAPTURED'
  | 'PAYMENT_CLOSE'
  | 'PAYMENT_NEW_SALES';

export interface PaymentEvent {
  eventType: PaymentEventType; // Type of event
  processorName: string; // Payment provider name
  paymentMethod: string;
  userMessage?: string; // User friendly Message
  data: Record<string, any>; // Event-specific data
}

export interface PaymentRequestPreparingEvent extends PaymentEvent {
  eventType: 'PAYMENT_REQUEST_PREPARING';
  data: {
    originalAmount: number;
    employeeReferenceId?: string;
    customer?: Record<string, any>;
    ticketId: string;
    ticketAlphaNumId: string;
  };
}

export interface PaymentCreatedEvent extends PaymentEvent {
  eventType: 'PAYMENT_CREATED';
  data: {
    id: string;
  };
}

export interface PaymentLinkCreatedEvent extends PaymentEvent {
  eventType: 'PAYMENT_LINK_CREATED';
  data: {
    paymentLink: string;
  };
}

export interface PaymentCanceledEvent extends PaymentEvent {
  eventType: 'PAYMENT_CANCELED';
  data: {};
}

export interface PaymentCloseEvent extends PaymentEvent {
  eventType: 'PAYMENT_CLOSE';
  data: {};
}

export interface PaymentNewSalesEvent extends PaymentEvent {
  eventType: 'PAYMENT_NEW_SALES';
  data: {};
}

export interface PaymentErrorEvent extends PaymentEvent {
  eventType: 'PAYMENT_ERROR';
  userMessage: string;
  data: {
    errorCode?: string;
    errorMessage?: string;
    paymentId?: string;
  };
}

export interface PaymentApprovedEvent extends PaymentEvent {
  eventType: 'PAYMENT_APPROVED';
  data: {
    paymentId: string;
  };
}

export interface PaymentSubmittedEvent extends PaymentEvent {
  eventType: 'PAYMENT_SUBMITTED';
  data: {
    paymentId: string;
  };
}

export interface PaymentCapturedEvent extends PaymentEvent {
  eventType: 'PAYMENT_CAPTURED';
  data: {
    paymentId: string;
  };
}

export interface AchPaymentCreatedEvent extends PaymentEvent {
  eventType: 'PAYMENT_CREATED';
  data: {
    status: 'created' | 'pending' | 'confirmed';
    paymentLink?: string;
  };
}

export type PaymentEvents =
  | PaymentCreatedEvent
  | PaymentCanceledEvent
  | PaymentErrorEvent
  | PaymentApprovedEvent;

export type PaymentEventHandler = (event: PaymentEvents) => void;

export type PaymentOptionSelected = 'PAYMENT_OPTION_SELECTED';

export interface PaymentOptionSelectedEvent {
  eventType: PaymentOptionSelected;
  data: {
    option: string;
  };
}

export function generatePaymentOptionSelectedEvent(
  option: string
): PaymentOptionSelectedEvent {
  const eventPayload: PaymentOptionSelectedEvent = {
    eventType: 'PAYMENT_OPTION_SELECTED',
    data: {
      option,
    },
  };

  return eventPayload;
}
