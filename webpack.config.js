const path = require('path');
const webpack = require('webpack');
var copyWebpackPlugin = require('copy-webpack-plugin');
const Dotenv = require('dotenv-webpack');
const { execSync } = require('child_process');

const bundleOutputDir = './dist';
const isSandbox = process.env.SANDBOX === 'true';
const isPreprod = process.env.PREPROD === 'true';
const isDevBuild = isSandbox || isPreprod;
const isLocal = process.env.LOCAL_ENV === 'true';

// Get the latest Git tag associated with the current branch
const gitTag = execSync('git describe --tags').toString().trim();

const assetsBaseUrl = isLocal
  ? '/assets'
  : isSandbox
    ? 'https://js.sandbox.treezpay.com/assets/'
    : isPreprod
      ? 'https://js.dev.treezpay.com/assets/'
      : 'https://js.treezpay.com/assets/';

const copyFilesPatterns = [
  { from: 'dev' },
  { from: 'src/assets', to: 'assets' },
];

module.exports = (env, args) => {
  return [
    {
      entry: './src/index.ts',
      output: {
        filename: 'widget.js',
        path: path.resolve(bundleOutputDir),
      },
      devServer: {
        contentBase: bundleOutputDir,
      },
      plugins: isDevBuild
        ? [
            ...(isDevBuild && !isPreprod
              ? [new webpack.SourceMapDevToolPlugin()]
              : []),
            new copyWebpackPlugin({
              patterns: copyFilesPatterns,
            }),
            new Dotenv({
              path: isSandbox ? `./.env.sandbox` : `./.env.development`,
            }),
            new webpack.DefinePlugin({
              'process.env.GIT_TAG': JSON.stringify(gitTag),
              'process.env.PREPROD': JSON.stringify(process.env.PREPROD),
              'process.env.SANDBOX': JSON.stringify(process.env.SANDBOX),
              'process.env.LOCAL_ENV': JSON.stringify(process.env.LOCAL_ENV),
            }),
          ]
        : [
            new Dotenv({
              path: `./.env.production`,
            }),
            new webpack.DefinePlugin({
              'process.env.GIT_TAG': JSON.stringify(gitTag),
            }),
            new copyWebpackPlugin({
              patterns: copyFilesPatterns,
            }),
          ],
      optimization: {
        minimize: !isDevBuild || isPreprod,
      },
      mode: isDevBuild ? 'development' : 'production',
      module: {
        rules: [
          // packs SVG's discovered in url() into bundle

          {
            test: /\.(png|jpe?g|gif)$/i, // Regex to match image files
            use: [
              {
                loader: 'url-loader',
                options: {
                  limit: 30000, // 30kb
                  fallback: {
                    loader: 'file-loader',
                    options: {
                      name: '[name].[ext]',
                      publicPath: assetsBaseUrl, // Base URL for assets
                      outputPath: 'assets/',
                    },
                  },
                },
              },
            ],
          },
          { test: /\.svg/, use: 'svg-url-loader' },
          {
            test: /\.css$/i,
            use: [
              {
                loader: 'style-loader',
                options: { injectType: 'singletonStyleTag' },
              },
              {
                // allows import CSS as modules
                loader: 'css-loader',
                options: {
                  modules: {
                    // css class names format
                    localIdentName: 'tz-[name]-[local]',
                  },
                },
              },
            ],
          },
          // use babel-loader for TS and JS modeles,
          // starting v7 Babel babel-loader can transpile TS into JS,
          // so no need for ts-loader
          // note, that in dev we still use tsc for type checking
          {
            test: /\.(js|ts|tsx|jsx)$/,
            exclude: /node_modules/,
            use: [
              {
                loader: 'babel-loader',
                options: {
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        targets: {
                          browsers: ['IE 11, last 2 versions'],
                          esmodules: true,
                        },
                        // Update the useBuiltIns option and add corejs version
                        useBuiltIns: 'entry',
                        corejs: 3,
                      },
                    ],
                    [
                      // enable transpiling ts => js
                      '@babel/typescript',
                      // tell babel to compile JSX using into Preact
                      { jsxPragma: 'h' },
                    ],
                  ],
                  plugins: [
                    // syntax sugar found in React components
                    '@babel/proposal-class-properties',
                    '@babel/proposal-object-rest-spread',
                    // transpile JSX/TSX to JS
                    [
                      '@babel/plugin-transform-react-jsx',
                      {
                        // we use Preact, which has `Preact.h` instead of `React.createElement`
                        pragma: 'h',
                        pragmaFrag: 'Fragment',
                      },
                    ],
                  ],
                },
              },
            ],
          },
        ],
      },
      resolve: {
        extensions: ['.*', '.js', '.ts', '.tsx'],
      },
    },
  ];
};
