<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebSocket Test - TreezPay</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
        font-weight: bold;
      }
      .status.connecting {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }
      .status.connected {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .status.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .status.closed {
        background-color: #e2e3e5;
        color: #383d41;
        border: 1px solid #d6d8db;
      }
      .log {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        margin: 10px 0;
        height: 300px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 14px;
      }
      .controls {
        margin: 20px 0;
      }
      button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
        font-size: 14px;
      }
      button:hover {
        background-color: #0056b3;
      }
      button:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }
      .message-input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        margin: 10px 0;
        font-family: 'Courier New', monospace;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>WebSocket Test - TreezPay</h1>

      <div id="status" class="status connecting">Disconnected</div>

      <div class="controls">
        <button id="connectBtn" onclick="connect()">Connect</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>
          Disconnect
        </button>
        <button onclick="clearLog()">Clear Log</button>
      </div>

      <div>
        <h3>Send Custom Message:</h3>
        <textarea
          id="messageInput"
          class="message-input"
          rows="4"
          placeholder='{"action": "ping", "data": "test"}'
        ></textarea>
        <button id="sendBtn" onclick="sendMessage()" disabled>
          Send Message
        </button>
      </div>

      <div>
        <h3>Connection Log:</h3>
        <div id="log" class="log"></div>
      </div>
    </div>

    <script>
      let websocket = null;
      const logElement = document.getElementById('log');
      const statusElement = document.getElementById('status');
      const connectBtn = document.getElementById('connectBtn');
      const disconnectBtn = document.getElementById('disconnectBtn');
      const sendBtn = document.getElementById('sendBtn');

      // Logger object to mimic your original code
      const logger = {
        log: function (message) {
          console.log(message);
          addToLog(`[LOG] ${message}`);
        },
      };

      function addToLog(message) {
        const timestamp = new Date().toLocaleTimeString();
        logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        logElement.scrollTop = logElement.scrollHeight;
      }

      function updateStatus(status, className) {
        statusElement.textContent = status;
        statusElement.className = `status ${className}`;
      }

      function connect() {
        if (websocket && websocket.readyState === WebSocket.OPEN) {
          addToLog('WebSocket is already connected');
          return;
        }

        updateStatus('Connecting...', 'connecting');
        addToLog('Attempting to connect to wss://ws.dev.treezpay.com');

        websocket = new WebSocket('wss://ws.dev.treezpay.com');

        websocket.onopen = async () => {
          logger.log('WebSocket connection opened');
          updateStatus('Connected', 'connected');

          const token =
            'eyJhdWQiOiJodHRwczovL3BheW1lbnQtZ2F0ZXdheS1hcGkuZGV2LnRyZWV6cGF5LmNvbS92MC9wYXltZW50L3Nkay9wcm9jZXNzb3JzL2ZmZCIsImlzcyI6ImIzODBlYTM2LTM2ZDYtNGNkNi1hYmRkLWRkYjgwNGYxMmEzZiIsImlhdCI6MTc0OTU0Mjk3NjkyNCwiZXhwIjoxNzQ5NTQzMDA2OTI0LCJvaWQiOiI1ZmUwNTY0Mi1hNzIwLTQzY2MtYTliMi0xNzhkZTZiM2IxYTkifQ==.yIswTIEUfLNjsU/AMvrvx/UHwitCSClP+qu/penMnSG2Z5I4sY8YdUx1GM80uCiXk251gnGa9IW4V/QcqolA1TOZ+ZAcaaiJplhkOcjBNatfSkjWv1NUsthevwHyD57WGAL5BVw6zcajYYOqJlE5LW3LI40eF/lQXyfhyF6zj5JOo5q1u3SFLxgNWcZ/rMDRao5DqmZ71nzPIK6moH3ezE4+dZzOjfd5ykOtlbtpICCVPbftrM5QlW+sGHbOirAVq/RENjboQyY0aL9G5uXQ9XeZL3tuXMC44OKZBIJMzh+m5Z2MhWxIV5gT0D6qvUc/93HDK2m/66UHpbDciFhIc/0XU93sq2GOxrLI3SkjYD7iclSkYGzecD2LYWSMCUHGzqgo5jShU08uZ4cpk6WCRzCli2Sc5d2H82lFS4SK5u0buAFBzl49UM4P9MDj8xtWmgJsjNFryLG95AisVq4GDAAQEzVFZ5/LxjtETUhUXtRV9szoy9cAADgH1Qlhh0zTQurFfT1HVWhGTifV971aZ2ogGTfLMIPoeJsqUJEifofoAqmeqjk7tOIDwQDtNoRmKQcdGJD9RjzY4ugCqmLemwyBzAMr1hieTuoIhPgu0ZQfb2PHtH2dneXn4s1FKVjBDv25m0Z55O2BKzt12Rr2SxqZLhlr058lxFt5fCsijpk=';
          if (token) {
            const authMessage = JSON.stringify({
              sessionId: '8e01bf41-9533-4858-a3fd-29a8d0352ef3',
              action: 'authenticate',
              token: token,
              type: 'partner',
            });

            addToLog(`Sending authentication message: ${authMessage}`);
            websocket.send(authMessage);
          }

          connectBtn.disabled = true;
          disconnectBtn.disabled = false;
          sendBtn.disabled = false;
        };

        websocket.onmessage = event => {
          addToLog(`Received message: ${event.data}`);
          try {
            const data = JSON.parse(event.data);
            addToLog(`Parsed JSON: ${JSON.stringify(data, null, 2)}`);
          } catch (e) {
            addToLog(`Could not parse as JSON: ${e.message}`);
          }
        };

        websocket.onerror = error => {
          addToLog(`WebSocket error: ${error}`);
          updateStatus('Error', 'error');
        };

        websocket.onclose = event => {
          addToLog(
            `WebSocket closed. Code: ${event.code}, Reason: ${event.reason || 'No reason provided'}`
          );
          updateStatus('Disconnected', 'closed');

          connectBtn.disabled = false;
          disconnectBtn.disabled = true;
          sendBtn.disabled = true;
        };
      }

      function disconnect() {
        if (websocket) {
          websocket.close();
          addToLog('Disconnection requested');
        }
      }

      function sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();

        if (!message) {
          addToLog('No message to send');
          return;
        }

        if (websocket && websocket.readyState === WebSocket.OPEN) {
          try {
            // Validate JSON before sending
            JSON.parse(message);
            websocket.send(message);
            addToLog(`Sent message: ${message}`);
          } catch (e) {
            addToLog(`Invalid JSON message: ${e.message}`);
          }
        } else {
          addToLog('WebSocket is not connected');
        }
      }

      function clearLog() {
        logElement.innerHTML = '';
      }

      // Auto-connect on page load
      window.onload = function () {
        addToLog('Page loaded. Ready to connect.');
      };
    </script>
  </body>
</html>
