{"name": "treez-paysdk", "scripts": {"build": "webpack --config webpack.config.js", "sandbox-release": "SANDBOX=true webpack --config webpack.config.js --mode development", "build-release": "PREPROD=true webpack --config webpack.config.js --mode production", "production-release": "webpack --config webpack.config.js --mode production", "start": "PREPROD=true LOCAL_ENV=true webpack serve --open", "lint": "tslint --project tsconfig.json", "test": "jest --ci --coverage", "test:unit:ci": "jest --ci --coverage", "test:integration:ci": "jest --ci --coverage"}, "author": "TreezPay Team", "devDependencies": {"@babel/core": "^7.8.3", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-proposal-object-rest-spread": "^7.12.13", "@babel/plugin-transform-react-jsx": "^7.12.13", "@babel/plugin-transform-typescript": "^7.12.13", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.12.13", "@babel/preset-typescript": "^7.12.13", "@testing-library/jest-dom": "^6.5.0", "@testing-library/preact": "^3.2.4", "@testing-library/react": "^16.0.1", "@types/jest": "^25.2.3", "@types/qrcode": "^1.5.5", "@types/testing-library__jest-dom": "^6.0.0", "babel-loader": "8.2.2", "compression-webpack-plugin": "^3.1.0", "copy-webpack-plugin": "10.2.4", "css-loader": "^3.4.2", "dotenv-webpack": "^8.1.0", "file-loader": "^6.2.0", "identity-obj-proxy": "^3.0.0", "jest": "^26.6.3", "jest-junit": "^16.0.0", "prettier": "^3.3.3", "style-loader": "^1.1.3", "svg-url-loader": "^3.0.3", "ts-jest": "^25.5.1", "tslint": "^5.20.1", "typescript": "4.7.3", "url-loader": "^4.1.1", "webpack": "^5.20.2", "webpack-cli": "^4.5.0", "webpack-dev-server": "^3.11.2"}, "dependencies": {"@babel/plugin-transform-destructuring": "^7.12.13", "@sentry/react": "^8.35.0", "@types/react": "^18.3.7", "axios": "^0.21.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "clsx": "^1.1.0", "core-js": "^3.38.1", "preact": "10.24.0", "qrcode": "^1.5.4", "reflect-metadata": "^0.2.2", "uuid": "7.0.3", "wouter-preact": "^3.3.5"}, "jest": {"moduleFileExtensions": ["js", "ts", "tsx"], "rootDir": "./", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "jsdom"}}