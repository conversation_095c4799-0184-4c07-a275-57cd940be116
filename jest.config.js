module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'jsdom',
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
    moduleNameMapper: {
      '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
      '\\.(gif|ttf|eot|svg|png)$': '<rootDir>/__mocks__/fileMock.js',
    },
    setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  
    // Configurations for test artifacts
  
    // Generate JUnit XML report
    reporters: [
      'default',
      ['jest-junit', {
        outputDirectory: '.',
        outputName: 'junit.xml',
      }],
    ],
  
    // Generate coverage report in Cobertura format
    collectCoverage: true,
    coverageReporters: ['cobertura', 'text', 'lcov'],
    coverageDirectory: 'coverage',
  
    // Set coverage thresholds
    // coverageThreshold: {
    //   global: {
    //     branches: 80,
    //     functions: 80,
    //     lines: 80,
    //     statements: 80,
    //   },
    // },
  };
