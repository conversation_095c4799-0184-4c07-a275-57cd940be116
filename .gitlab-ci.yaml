.global_cache: &global_cache
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
  policy: pull-push

.unit_test:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG == null'
  image: node:20.17.0
  allow_failure: true
  before_script:
    - mkdir coverage
    - npm install
  script:
    - |
      echo "Run Unit tests"
      npm run test:unit:ci
  cache:
    <<: *global_cache
  coverage: /All files[^|]*\|[^|]*\s+([\d\.]+)/
  artifacts:
    when: always
    reports:
      junit: junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml

.integration_test:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG == null'
  image: node:20.17.0
  allow_failure: true
  before_script:
    - mkdir coverage
    - npm install
  script:
    - |
      echo "Run Integration tests"
      npm run test:integration:ci
  cache:
    <<: *global_cache
  coverage: /All files[^|]*\|[^|]*\s+([\d\.]+)/
  artifacts:
    when: always
    reports:
      junit: junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml

.regression_test:
  image: cypress/base:20.17.0
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule"' 
  variables:
  before_script:
    - npm install
    - npm install cypress cy2
    - npm install mocha mochawesome mochawesome-merge mochawesome-report-generator serve kill-port
    - npm run build-release
  script:
    - |
      npx serve out -l 3000 &
      npx wait-on http://localhost:3000
      npm run test:e2e:preprod
      npm run test:report:merge
      npm run test:report:generate
      npx kill-port 3000
  cache:
    <<: *global_cache
  artifacts:
    when: always
    paths:
      - cypress/videos/*.mp4

.notify_slack:
  image: alpine:latest
  script: |  
    apk add --no-cache curl
    MESSAGE="The scheduled regression test has failed in $CI_PROJECT_NAME on branch 'preprod'! Please check the pipeline $CI_PIPELINE_URL for more details."
    curl -X POST -H 'Content-type: application/json' --data "{\"text\":\"$MESSAGE\"}" ${SLACK_WEBHOOK_URL}

tag:
  image: node:20.17.0
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  script:
    - |
      export RELEASE_VERSION=$(node -pe "require('./package.json')['version']")
      echo $RELEASE_VERSION > version.txt
      git clone https://${GIT_CI_USERNAME}:${GIT_TOKEN}@gitlab.com/${CI_PROJECT_PATH}.git target-repo
      cd target-repo
      git checkout $CI_COMMIT_BRANCH # Commit branch is used incase a team wants to overwrite the rule for default branch.
      git config user.name $GIT_CI_USERNAME
      git config user.email $GIT_CI_EMAIL
      git tag v$RELEASE_VERSION $CI_COMMIT_SHA
      git push origin v$RELEASE_VERSION # Push Tag

.release:
  image: registry.gitlab.com/gitlab-org/release-cli:latest
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - echo "running release_job for tag $CI_COMMIT_TAG"
  release:      
    tag_name: '$CI_COMMIT_TAG'
    description: '$CI_COMMIT_TAG'
    ref: '$CI_COMMIT_SHA'   
