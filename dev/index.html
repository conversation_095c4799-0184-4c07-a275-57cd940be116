<!doctype html>
<html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta charset="UTF-8" />
    <title>PaySDK Widget Sandbox Playground</title>
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png" />
    <link rel="preload" href="./widget.js" as="script" />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
      rel="stylesheet"
    />
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0px;
      }

      h1,
      h2 {
        text-align: center;
      }

      .container {
        max-width: 700px;
      }

      .input-group {
        margin-bottom: 15px;
      }

      .input-group label {
        display: inline-block;
        width: 200px;
        vertical-align: top;
        margin-top: 6px;
      }

      .input-group input,
      .input-group textarea {
        width: calc(100% - 210px);
        padding: 6px;
      }

      .input-group-row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 15px;
      }

      .input-group-row .input-item {
        margin-right: 10px;
      }

      .input-group-row .input-item:last-child {
        margin-right: 0;
      }

      .input-group-row .input-item label {
        display: block;
        margin-bottom: 5px;
      }

      .input-group-row .input-item input {
        width: 100%;
        padding: 6px;
      }

      button {
        padding: 10px 20px;
        margin: 5px;
        cursor: pointer;
      }

      #paymentCommand {
        width: 100%;
        font-family: monospace;
        padding: 10px;
      }

      .code-display {
        background-color: #f9f9f9;
        padding: 10px;
        border: 1px solid #ccc;
        font-family: monospace;
        overflow-x: auto;
      }

      #tokenWarning {
        color: red;
        font-weight: bold;
      }

      /* Add a border to the widget area */
      #paysdk-widget {
        border: 1px solid #ccc;
        padding: 10px;
      }

      #paysdk-widget .tz-ecommerce-root {
        position: fixed;
        right: 100px;
        padding: 16px;
        width: 500px;
        top: 100px;
        background: #dfe6f0;
      }

      #paysdk-widget .tz-ffd-root,
      #paysdk-widget .tz-virtualTerminal-root {
        position: fixed;
        right: 20px;
      }

      .dark-theme #paysdk-widget .tz-ffd-scope,
      .dark-theme #paysdk-widget .tz-virtualTerminal-scope {
        --tz-main-bg: #1a1a1a;
        --tz-color-error: #ff4444;
        --tz-color-primary: #8ccc52;
        --tz-color-primary-hover: #7ab842;
        --tz-color-disabled: #404040;
        --tz-color-border: #333333;
        --tz-text-color-primary: #ffffff;
        --tz-text-color-secondary: #999999;
        --tz-font-family: 'Roboto', sans-serif;
      }

      .dark-theme #paysdk-widget .tz-totalDisplay-container {
        background: #262626;
      }
      .dark-theme #paysdk-widget .tz-totalDisplay-breakdown {
        background-color: #262626;
      }
      .dark-theme #paysdk-widget .tz-overlay-lightBg {
        background-color: #1a1a1a;
      }
      .dark-theme #paysdk-widget .tz-tip-ctaContainer {
        background: #1a1a1a;
      }

      .dark-theme #paysdk-widget .tz-titlebar-title {
        color: #cbd5e0;
      }
      .dark-theme #paysdk-widget .tz-totalDisplay-headerTotalAmt {
        color: #a0aec0;
      }
      .dark-theme #paysdk-widget .tz-totalDisplay-totalAmount {
        color: #e2e8f0;
      }

      .dark-theme #paysdk-widget .tz-titlebar-root {
        border-bottom: 1px solid #333333;
      }

      .dark-theme #paysdk-widget .tz-totalDisplay-line {
        background: #4a5568;
      }
      .dark-theme #paysdk-widget .tz-totalDisplay-payingVia {
        border: 1px solid #4a5568;
      }

      .dark-theme #paysdk-widget .tz-button-secondary {
        background-color: #262626;
        color: #8ccc52;
      }

      .dark-theme #paysdk-widget .tz-button-secondary:hover {
        background-color: #333333;
      }

      .dark-theme #paysdk-widget .tz-button-danger {
        background-color: #262626;
      }

      .dark-theme #paysdk-widget .tz-tip-selectedTipButton {
        background: #1a4007;
        border: 3px solid #8ccc52;
      }

      .dark-theme #paysdk-widget .tz-QRCode-canvas {
        color: #1a4007;
        background-color: #1a4007;
      }

      .dark-theme #paysdk-widget .tz-tip-tipButton,
      .dark-theme #paysdk-widget input.tz-tip-tipButton {
        background-color: #262626;
      }
    </style>
  </head>

  <body>
    <div id="paysdk-widget"></div>
    <div id="paysdk-widget-banner" style="width: 800px"></div>

    <div class="container">
      <h1>PaySDK Widget Sandbox Playground</h1>
      <h2>The shown widget is for demonstration purposes only</h2>

      <!-- Widget Initialization Code -->
      <h3>Widget Initialization Code:</h3>
      <pre id="widgetInitialization" class="code-display">
(function (w, d, s, o, f, js, fjs) {
    w[o] = w[o] || function () { (w[o].q = w[o].q || []).push(arguments) };
    js = d.createElement(s), fjs = d.getElementsByTagName(s)[0];
    js.id = o; js.src = f; js.async = 1; fjs.parentNode.insertBefore(js, fjs);
}(window, document, 'script', '_treezpay', 'https://development.d28pm8dafanify.amplifyapp.com/widget.js'));

/*
    Script initialization
*/
_treezpay('init', {
    element: document.querySelector(`#paysdk-widget`),
    debug: true,
    authTokenFactory: () => Promise.resolve(getCookie('authToken')),
    getEntityId: () => Promise.resolve(document.getElementById('entityId').value), // Entity ID from input
    theme: {
        positionFixed: true,
    },
    dispensaryShortName: 'test-ach1'
});
        </pre
      >

      <p>
        Below you can initialize a payment by providing the necessary details.
      </p>

      <!-- Input Fields -->
      <div class="input-group">
        <label for="positionFixed">Position Fixed:</label>
        <select id="positionFixed" onchange="toggleFloatingButton()">
          <option value="true">True</option>
          <option value="false">False</option>
        </select>
      </div>

      <div class="input-group">
        <label for="darkTheme">Dark Theme:</label>
        <select id="darkTheme" onchange="toggleTheme()">
          <option value="true">On</option>
          <option value="false" selected>Off</option>
        </select>
      </div>

      <!-- Add new channel selector -->
      <div class="input-group">
        <label for="channel">Channel:</label>
        <select id="channel">
          <option value="ffd" selected>FFD</option>
          <option value="ecommerce">ECOMMERCE</option>
          <option value="virtual_terminal">VIRTUAL TERMINAL</option>
        </select>
      </div>

      <div class="input-group">
        <label for="originalAmount">Original Amount:</label>
        <input type="number" id="originalAmount" value="100" />
      </div>

      <div class="input-group-row">
        <div class="input-item">
          <label for="taxes">Taxes (optional):</label>
          <input type="number" id="taxes" value="0" />
        </div>
        <div class="input-item">
          <label for="discount">Discount (optional):</label>
          <input type="number" id="discount" value="0" />
        </div>
        <div class="input-item" style="display: none">
          <label for="rewardDollars">Reward Dollars (optional):</label>
          <input type="number" id="rewardDollars" value="0" />
        </div>
      </div>

      <div class="input-group">
        <label for="authToken">MSO Auth Token:</label>
        <textarea
          id="authToken"
          rows="4"
          cols="50"
          placeholder="Enter your auth token here..."
        ></textarea>
      </div>

      <div class="input-group">
        <label for="entityId">Entity ID:</label>
        <input
          type="text"
          id="entityId"
          value="200769e6-9cfa-4302-a60b-9cc41997925c"
        />
      </div>

      <div class="input-group">
        <label for="locationId">ST HW Location ID (optional):</label>
        <input
          type="text"
          id="locationId"
          placeholder="Enter location ID (optional)"
        />
      </div>

      <div class="input-group">
        <label for="processorName">Processor Name (optional):</label>
        <input
          type="text"
          id="processorName"
          placeholder="Enter processor name (optional)"
        />
      </div>

      <div class="input-group">
        <label for="alphaNumId">Alpha Numeric ID (optional):</label>
        <input
          type="text"
          id="alphaNumId"
          placeholder="ABCD123 (auto-generated if blank)"
        />
      </div>

      <div class="input-group">
        <label for="ticketId">Ticket ID (optional):</label>
        <input
          type="text"
          id="ticketId"
          placeholder="UUID (auto-generated if blank)"
        />
      </div>

      <div class="input-group">
        <label for="entityName">Entity Name (optional):</label>
        <input
          type="text"
          id="entityName"
          placeholder="Enter entity name (optional)"
        />
      </div>

      <!-- Add new customer information fields -->
      <div class="input-group">
        <label for="customerFirstName">Customer First Name:</label>
        <input
          type="text"
          id="customerFirstName"
          value="John"
          placeholder="Enter customer first name"
        />
      </div>

      <div class="input-group">
        <label for="customerLastName">Customer Last Name:</label>
        <input
          type="text"
          id="customerLastName"
          value="Doe"
          placeholder="Enter customer last name"
        />
      </div>

      <div class="input-group">
        <label for="customerPhone">Customer Phone:</label>
        <input
          type="tel"
          id="customerPhone"
          value="5109886677"
          placeholder="Enter customer phone"
        />
      </div>

      <div class="input-group">
        <label for="customerEmail">Customer Email:</label>
        <input
          type="email"
          id="customerEmail"
          value="<EMAIL>"
          placeholder="Enter customer email"
        />
      </div>

      <!-- Warning for Token Expiry -->
      <div id="tokenWarning" style="display: none">
        Warning: The token was updated more than an hour ago!
      </div>

      <!-- Action Buttons -->
      <div>
        <button onclick="reloadWidget()">Refresh</button>
        <button id="startPaymentBtn" onclick="initPayment()">
          Start Payment
        </button>
        <button onclick="cancelPayment()">Cancel Payment</button>
        <br />
        <button onclick="reloadWidget()">Reload Widget</button>
        <br />
        <button onclick="mountBanner()">Mount banner</button>
        <button onclick="unmountBanner()">Unmount banner</button>
      </div>

      <!-- Display the command that gets executed -->
      <h3>Payment Command:</h3>
      <textarea id="paymentCommand" rows="10" readonly></textarea>
      <button onclick="copyCommand()">Copy Command</button>

      <!-- Display response from handlePaymentResponse -->
      <h3>Payment Response:</h3>
      <pre id="paymentResponse" class="code-display"></pre>

      <div class="input-group">
        <label for="isWebSocketEnabled">Use WebSocket:</label>
        <select id="isWebSocketEnabled" onchange="reloadWidget()">
          <option value="true" selected>On</option>
          <option value="false">Off</option>
        </select>
      </div>
    </div>

    <script>
      // Utility function to get a cookie by name
      function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
      }

      // Utility function to set a cookie
      function setCookie(name, value, hours) {
        const date = new Date();
        date.setTime(date.getTime() + hours * 60 * 60 * 1000);
        const expires = 'expires=' + date.toUTCString();
        document.cookie = name + '=' + value + ';' + expires + ';path=/';
      }

      // Function to generate a random alphanumeric string
      function generateRandomAlphaNum(length = 7) {
        const characters =
          'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
          result += characters.charAt(
            Math.floor(Math.random() * characters.length)
          );
        }
        return result;
      }

      // Function to generate a random UUID
      function generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
          /[xy]/g,
          function (c) {
            const r = (Math.random() * 16) | 0,
              v = c === 'x' ? r : (r & 0x3) | 0x8;
            return v.toString(16);
          }
        );
      }

      // utility to float start payment btn on full screen
      function toggleFloatingButton() {
        const positionFixed = document.getElementById('positionFixed').value;
        const button = document.getElementById('startPaymentBtn');

        if (positionFixed === 'false') {
          button.style.position = 'fixed';
          button.style.top = '5px';
          button.style.left = '5px';
          button.style.zIndex = '1000';
        } else {
          button.style.position = 'static';
          button.style.bottom = '';
          button.style.right = '';
          button.style.zIndex = '';
        }
      }

      // On page load, set the token if it exists in the cookie
      window.onload = function () {
        const savedToken = getCookie('authToken');
        const savedTime = getCookie('tokenTime');
        if (savedToken) {
          document.getElementById('authToken').value = savedToken;
          if (savedTime) {
            const tokenTime = new Date(savedTime);
            const currentTime = new Date();
            const timeDiff = (currentTime - tokenTime) / (1000 * 60 * 60); // Difference in hours
            if (timeDiff > 1) {
              document.getElementById('tokenWarning').style.display = 'block';
            }
          }
        }
      };

      function refresh() {
        const authToken = document.getElementById('authToken').value;
        setCookie('authToken', authToken, 24); // Save for 24 hours

        const command = `_treezpay('event', 'refresh');`;

        // Display the command in the textarea
        document.getElementById('paymentCommand').value = command;

        _treezpay('event', 'refresh');
      }

      function initPayment() {
        const originalAmount =
          parseFloat(document.getElementById('originalAmount').value) || 0;
        const taxes = parseFloat(document.getElementById('taxes').value) || 0;
        const discount =
          parseFloat(document.getElementById('discount').value) || 0;
        const rewardDollars =
          parseFloat(document.getElementById('rewardDollars').value) || 0;
        const authToken = document.getElementById('authToken').value;
        const entityId = document.getElementById('entityId').value;
        const entityName = document.getElementById('entityName').value || null;
        const locationId = document.getElementById('locationId').value || null;
        const processorName =
          document.getElementById('processorName').value || null;
        const alphaNumId =
          document.getElementById('alphaNumId').value ||
          generateRandomAlphaNum();
        const ticketId =
          document.getElementById('ticketId').value || generateUUID();

        // Get customer information
        const customerFirstName =
          document.getElementById('customerFirstName').value || 'John';
        const customerLastName =
          document.getElementById('customerLastName').value || 'Doe';
        const customerPhone =
          document.getElementById('customerPhone').value || '5109886677';
        const customerEmail =
          document.getElementById('customerEmail').value ||
          '<EMAIL>';

        // Save the token and current time in cookies
        setCookie('authToken', authToken, 24); // Save for 24 hours
        setCookie('tokenTime', new Date().toISOString(), 24); // Save current time

        const command = `_treezpay('event', 'payment', {
    handlePaymentResponse: async (response) => {
        console.log(response);
    },
    originalAmount: ${originalAmount},
    taxes: ${taxes},
    discount: ${discount},
    rewardDollars: ${rewardDollars},
    employeeReferenceId: '777', // employee number
    transactingAssociateId: '149a2c2e-3baf-49f8-a931-56a6f5d8950a', // mso associate id
    customer: {
        id: 1,
        firstName: '${customerFirstName}',
        lastName: '${customerLastName}',
        phone: '${customerPhone}',
        email: '${customerEmail}',
    },
    ticketId: '${ticketId}',
    ticketAlphaNumId: '${alphaNumId}',
    locationId: ${locationId ? `'${locationId}'` : null},
    processorName: ${processorName ? `'${processorName}'` : null},
    entityName: '${entityName}'
});`;

        // Display the command in the textarea
        document.getElementById('paymentCommand').value = command;

        // Initialize the payment
        _treezpay('event', 'payment', {
          handlePaymentResponse: async response => {
            console.log(response);
            document.getElementById('paymentResponse').textContent =
              JSON.stringify(response, null, 2);
          },
          originalAmount: originalAmount,
          taxes: taxes,
          discount: discount,
          rewardDollars: rewardDollars,
          employeeReferenceId: '777',
          customer: {
            id: 1,
            firstName: customerFirstName,
            lastName: customerLastName,
            phone: customerPhone,
            email: customerEmail,
          },
          ticketId: ticketId,
          ticketAlphaNumId: alphaNumId,
          locationId: locationId,
          processorName: processorName,
          entityName: entityName,
        });
      }

      function cancelPayment() {
        _treezpay('event', 'cancel');

        const command = `_treezpay('event', 'cancel');`;
        document.getElementById('paymentCommand').value = command;
      }

      // Function to copy the command to the clipboard
      function copyCommand() {
        const paymentCommand = document.getElementById('paymentCommand');
        paymentCommand.select();
        paymentCommand.setSelectionRange(0, 99999); // For mobile devices
        document.execCommand('copy');
        alert('Command copied to clipboard!');
      }

      /*
            PaySDK injection
        */
      function unloadWidget(win, instanceName) {
        if (!win[`loaded-${instanceName}`]) {
          console.warn(`Widget with name [${instanceName}] is not loaded.`);
          return;
        }

        const targetElement = win.document.getElementById(
          `widget-${instanceName}`
        );
        if (targetElement) {
          targetElement.remove();
          console.log(`Removed widget element [${instanceName}] from the DOM.`);
        }

        const scriptElement = win.document.getElementById(instanceName);
        if (scriptElement) {
          scriptElement.remove();
          console.log(`Removed script element [${instanceName}] from the DOM.`);
        }

        delete win[`loaded-${instanceName}`];
        delete win[instanceName];

        console.log(
          `Unloaded widget instance [${instanceName}] and removed from window object.`
        );
      }

      function reloadWidget() {
        const positionFixed =
          document.getElementById('positionFixed').value === 'true';
        const channel = document.getElementById('channel').value;
        const isWebSocketEnabled =
          document.getElementById('isWebSocketEnabled').value === 'true';

        unloadWidget(window, '_treezpay');

        (function (w, d, s, o, f, js, fjs) {
          w[o] =
            w[o] ||
            function () {
              (w[o].q = w[o].q || []).push(arguments);
            };
          (js = d.createElement(s)), (fjs = d.getElementsByTagName(s)[0]);
          js.id = o;
          js.src = f;
          js.async = 1;
          fjs.parentNode.insertBefore(js, fjs);
        })(window, document, 'script', '_treezpay', './widget.js');

        _treezpay('init', {
          element: document.querySelector(`#paysdk-widget`),
          debug: true,
          isWebSocketEnabled: isWebSocketEnabled,
          authTokenFactory: () => Promise.resolve(getCookie('authToken')),
          getEntityId: () =>
            Promise.resolve(document.getElementById('entityId').value),
          theme: {
            positionFixed: positionFixed,
          },
          dispensaryShortName: 'test-ach1',
          channel: channel,
          autoRenderPayments: true,
          onReady: () => {
            // hook to initialize payment after the PaySDK is ready
            console.log('PaySDK is ready to be used');
          },
        });

        console.log(
          `Widget reloaded with positionFixed set to ${positionFixed}, channel ${channel}, and isWebSocketEnabled ${isWebSocketEnabled}`
        );
      }

      reloadWidget();

      function mountBanner() {
        _treezpay('event', 'mount', {
          component: 'payment-options-banner',
          element: document.querySelector(`#paysdk-widget-banner`),
        });
      }

      function unmountBanner() {
        _treezpay('event', 'unmount', { component: 'payment-options-banner' });
      }

      function toggleTheme() {
        const isDarkTheme =
          document.getElementById('darkTheme').value === 'true';
        document.body.classList.toggle('dark-theme', isDarkTheme);
      }
    </script>
  </body>
</html>
