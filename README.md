# TreezPay SDK Widget

## Web UI Framework

- **Preact**: A fast 3kB alternative to React with the same modern API.
  - [Preact Official Site](https://preactjs.com/)

## Routing

- **Wouter**: A tiny router for modern React and Preact apps that relies on Hooks.
  - [Wouter GitHub Repository](https://github.com/molefrog/wouter)

* Small footprint and solid snippet on hosting website (see [usage](#usage))
* Multi-instance on the same page
* Isolation of code execution and CSS
* Customization via configuration injection and API to Widget
* Minimal dependencies and small size via single request (>30KB gzipped)

and a few more.

## Dependencies

NodeJS v20.17.0

## Usage

In order to embed the widget add the following snippet at any location on the hosting page.

During initialization you can pass additional configurations to widget like so:

```html
<script>
  (function (w, d, s, o, f, js, fjs) {
    w[o] =
      w[o] ||
      function () {
        (w[o].q = w[o].q || []).push(arguments);
      };
    (js = d.createElement(s)), (fjs = d.getElementsByTagName(s)[0]);
    js.id = o;
    js.src = f;
    js.async = 1;
    fjs.parentNode.insertBefore(js, fjs);
  })(window, document, 'script', '_treezpay', './widget.js');
  _treezpay('init', {
    element: document.querySelector(`#paysdk-widget`),
    debug: true,
    authTokenFactory: () => Promise.resolve(getCookie('authToken')),
    entityId: document.getElementById('entityId').value, // Entity ID from input
    locationId: document.getElementById('locationId').value || '', // Optional Location ID
    theme: {
      positionFixed: true,
    },
  });
</script>
```

Send a command (event) to the sdk to initiate a payment:

```js
_treezpay('event', 'payment', {
  handlePaymentResponse: async response => {
    console.log(response);
  },
  originalAmount: parseFloat(originalAmount),
  referenceNumber: '48916c04-e0dc-4c19-8d5e-4e76d4069661',
  employeeReferenceId: '777',
  customer: {
    id: '1',
    firstName: 'Max',
    lastName: 'Test',
    phone: '5109889644',
    email: '<EMAIL>',
  },
  ticketId: 'ticket-b721-19199c977958',
  ticketAlphaNumId: 'test2-TICKET-xx7x-2',
  locationId: locationId || '', // Optional location ID
  processorName: processorName || '', // Optional payment method
  entityName: entityName || '' // Optional store name
});
```

Refresh payment configurations

```js
_treezpay('event', 'refresh');
```

You can find a full list of configurations in `AppConfigurations` interface.

## Develop

The widget dev setup is similar to regular client application. To get started:

```bash
npm i
npm start
```

This will open browser with "demo" page which hosts the widget.

## Deployment

CI follows the git-flow branching strategy through merges as below. AWS Amplify is used for deployment which includes provisioning a CloudFront distribution and S3 bucket for each environment. It also manages cache invalidation on CloudFront for each deployment.

- Deployment to sandbox: Merge PR to `development` branch ([Amplify app](https://us-west-2.console.aws.amazon.com/amplify/apps/d28pm8dafanify/overview)).
- Deployment to dev: Merge PR to `preprod` branch ([Amplify app](https://us-west-2.console.aws.amazon.com/amplify/apps/do76kimtaoqvf/overview)).
- Deployment to prod: Merge PR to `main` branch ([Amplify app](https://us-west-2.console.aws.amazon.com/amplify/apps/d5cxgnx5ekab6/overview)).

Deployment statuses are notified on the `#amplify-deployment-status` Slack channel.

**Hosted domains**

The minified and generated script output for the widget is hosted on the below URLs.

- Prod: https://js.treezpay.com/widget.js
- Dev: https://js.dev.treezpay.com/widget.js
- Sandbox: https://js.sandbox.treezpay.com/widget.js

## Integration

### PaySDKLoader.jsx: A React Component for Seamless Integration

This component makes it easy to integrate the payment SDK into your React-based application.
